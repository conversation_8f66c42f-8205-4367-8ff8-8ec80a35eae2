"""
多关键词高亮处理模块

支持在一次执行中对多个关键词使用不同颜色进行高亮处理。
"""

import json
import re
import difflib
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass

import pymupdf

from pdf_highlighter.core.highlighter import PDFHighlighter
from pdf_highlighter.core.validator import ParameterValidator
from pdf_highlighter.utils.logger import get_logger
from pdf_highlighter.utils.exceptions import ValidationError, ConfigurationError


@dataclass
class HighlightRegion:
    """高亮区域数据结构"""
    page_num: int
    quad: pymupdf.Quad
    keyword: str
    keyword_id: str
    color: Tuple[float, float, float]
    opacity: float
    priority: int
    annotation_style: str = "highlight"  # 标记样式，默认为高亮
    applied: bool = False  # 是否已应用高亮

    def overlaps_with(self, other: 'HighlightRegion') -> bool:
        """检查是否与另一个高亮区域重叠"""
        if self.page_num != other.page_num:
            return False
        return self.quad.rect.intersects(other.quad.rect)


@dataclass
class KeywordConfig:
    """关键词配置类"""
    text: str
    color: Union[str, tuple] = "yellow"
    opacity: float = 0.5
    case_sensitive: bool = False
    regex: bool = False
    whole_word: bool = False
    occurrence: Optional[int] = None
    pages: Optional[str] = None  # 页面范围字符串，如 "1,3-5,7"
    context_phrase: Optional[str] = None  # 上下文短语，如 "添加以供审核"
    priority: Optional[int] = None  # 优先级，数字越小优先级越高
    id: Optional[str] = None  # 关键字唯一标识符
    similarity_threshold: Optional[float] = None  # 上下文相似度阈值，覆盖全局设置
    annotation_style: str = "highlight"  # 标记样式，默认为高亮
    fuzzy_enabled: Optional[bool] = None  # 是否启用模糊匹配，None表示使用全局设置
    fuzzy_start_chars: Optional[int] = None  # 模糊匹配开头字符数，None表示使用全局设置
    fuzzy_end_chars: Optional[int] = None  # 模糊匹配结尾字符数，None表示使用全局设置

    def __post_init__(self):
        """验证配置参数"""
        self.text = ParameterValidator.validate_keyword(self.text)
        self.color = ParameterValidator.validate_color(self.color)
        self.opacity = ParameterValidator.validate_opacity(self.opacity)
        self.annotation_style = ParameterValidator.validate_annotation_style(self.annotation_style)

        # 验证上下文短语（使用警告而不是错误）
        if self.context_phrase and self.text not in self.context_phrase:
            # 记录警告但不抛出错误，允许降级处理
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"关键词 '{self.text}' 不在上下文短语 '{self.context_phrase}' 中，"
                         f"将在搜索时进行降级处理")
            # 不清除上下文短语，让搜索阶段处理

        # 设置默认优先级（基于关键词长度，长关键词优先级更高）
        if self.priority is None:
            self.priority = 1000 - len(self.text)  # 长度越长，优先级数字越小

        # 自动生成ID（如果未提供）
        if self.id is None:
            import uuid
            import hashlib
            # 基于关键词内容生成确定性的短ID
            content = f"{self.text}_{self.context_phrase or ''}_{self.pages or ''}"
            hash_obj = hashlib.md5(content.encode('utf-8'))
            self.id = f"kw_{hash_obj.hexdigest()[:8]}"


class MultiKeywordHighlighter:
    """多关键词高亮处理器
    
    支持在一次执行中对多个关键词使用不同颜色进行高亮。
    """
    
    def __init__(self, pdf_path: str, context_fallback_strategy: str = "fallback_to_normal"):
        """初始化多关键词高亮处理器

        Args:
            pdf_path: PDF文件路径
            context_fallback_strategy: 上下文短语不存在时的降级策略
                - "fallback_to_normal": 降级为普通关键字搜索（默认）
                - "skip": 跳过该关键字
                - "warn_only": 仅记录警告，返回空结果
        """
        self.pdf_path = ParameterValidator.validate_pdf_path(pdf_path)
        self.logger = get_logger()
        self.keyword_configs: List[KeywordConfig] = []
        self.global_options: Dict[str, Any] = {}
        self.context_fallback_strategy = context_fallback_strategy
        self.global_similarity_threshold = 1.0  # 默认100%匹配

        # 模糊匹配全局配置
        self.fuzzy_matching_enabled = False  # 默认关闭模糊匹配
        self.fuzzy_start_chars = 2  # 默认开头字符数
        self.fuzzy_end_chars = 2  # 默认结尾字符数
        
    def add_keyword(self, text: str, color: Union[str, tuple] = "yellow",
                   opacity: float = 0.5, **options) -> str:
        """添加关键词配置

        Args:
            text: 关键词文本
            color: 高亮颜色
            opacity: 透明度
            **options: 其他选项，包括 pages、context_phrase、priority、id、annotation_style、
                      fuzzy_enabled、fuzzy_start_chars、fuzzy_end_chars

        Returns:
            str: 关键词的ID
        """
        config = KeywordConfig(
            text=text,
            color=color,
            opacity=opacity,
            case_sensitive=options.get('case_sensitive', False),
            regex=options.get('regex', False),
            whole_word=options.get('whole_word', False),
            occurrence=options.get('occurrence'),
            pages=options.get('pages'),
            context_phrase=options.get('context_phrase'),
            priority=options.get('priority'),
            id=options.get('id'),
            similarity_threshold=options.get('similarity_threshold'),
            annotation_style=options.get('annotation_style', 'highlight'),
            fuzzy_enabled=options.get('fuzzy_enabled'),
            fuzzy_start_chars=options.get('fuzzy_start_chars'),
            fuzzy_end_chars=options.get('fuzzy_end_chars')
        )
        self.keyword_configs.append(config)
        pages_info = f" (页面: {config.pages})" if config.pages else ""
        context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else ""
        priority_info = f" (优先级: {config.priority})" if options.get('priority') is not None else ""
        id_info = f" (ID: {config.id})"

        # 检查上下文配置的有效性
        if options.get('context_phrase') and config.context_phrase and text not in config.context_phrase:
            self.logger.warning(f"关键词 '{text}' 的上下文配置可能无效，将在搜索时进行降级处理")

        self.logger.info(f"添加关键词配置: '{text}' -> {color}{pages_info}{context_info}{priority_info}{id_info}")
        return config.id

    def _normalize_text(self, text: str) -> str:
        """标准化文本，用于相似度计算

        Args:
            text: 原始文本

        Returns:
            str: 标准化后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        normalized = re.sub(r'\s+', ' ', text.strip())
        return normalized

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            float: 相似度值，范围0-1
        """
        if not text1 or not text2:
            return 0.0

        # 标准化文本
        norm_text1 = self._normalize_text(text1)
        norm_text2 = self._normalize_text(text2)

        if norm_text1 == norm_text2:
            return 1.0

        # 使用SequenceMatcher计算相似度
        similarity = difflib.SequenceMatcher(None, norm_text1, norm_text2).ratio()
        return similarity

    def _find_similar_context(self, highlighter, context_phrase: str,
                             similarity_threshold: float, pages: Optional[List[int]] = None) -> List[Tuple[int, str, float]]:
        """在PDF中查找相似的上下文短语

        Args:
            highlighter: PDF高亮器实例
            context_phrase: 目标上下文短语
            similarity_threshold: 相似度阈值
            pages: 搜索的页面范围

        Returns:
            List[Tuple[int, str, float]]: 找到的相似上下文列表，格式为(页码, 匹配文本, 相似度)
        """
        similar_contexts = []

        # 如果阈值为1.0，使用精确匹配（向后兼容）
        if similarity_threshold >= 1.0:
            exact_results = highlighter.search_keywords(context_phrase, pages=pages)
            for page_num, quads in exact_results:
                if quads:  # 如果找到精确匹配
                    similar_contexts.append((page_num, context_phrase, 1.0))
            return similar_contexts

        # 模糊匹配：在每个页面中搜索相似文本
        target_pages = pages if pages else range(1, len(highlighter.doc) + 1)

        for page_num in target_pages:
            try:
                page = highlighter.doc[page_num - 1]
                page_text = page.get_text()

                # 将页面文本分割成句子或短语进行匹配
                sentences = self._split_text_into_phrases(page_text, len(context_phrase))

                for sentence in sentences:
                    similarity = self._calculate_similarity(context_phrase, sentence)
                    if similarity >= similarity_threshold:
                        similar_contexts.append((page_num, sentence, similarity))

            except Exception as e:
                self.logger.warning(f"在页面 {page_num} 搜索相似上下文时出错: {e}")
                continue

        return similar_contexts

    def _select_best_similar_context(self, similar_contexts: List[Tuple[int, str, float]]) -> List[Tuple[int, str, float]]:
        """从相似上下文列表中选择相似度最高的段落
        
        Args:
            similar_contexts: 相似上下文列表，格式为 List[Tuple[int, str, float]]
                           元组格式为 (页码, 匹配文本, 相似度)
        
        Returns:
            List[Tuple[int, str, float]]: 只包含相似度最高的段落的列表
        """
        if not similar_contexts:
            return []
        
        # 找到最高相似度
        max_similarity = max(similarity for _, _, similarity in similar_contexts)
        
        # 选择所有具有最高相似度的段落
        best_contexts = [
            context for context in similar_contexts
            if context[2] >= max_similarity
        ]
        
        # 如果有多个段落具有相同的最高相似度，选择第一个找到的段落
        if len(best_contexts) > 1:
            self.logger.debug(f"找到 {len(best_contexts)} 个相同最高相似度 ({max_similarity:.3f}) 的段落，选择第一个")
            return [best_contexts[0]]
        
        return best_contexts

    def _split_text_into_phrases(self, text: str, target_length: int) -> List[str]:
        """将文本分割成短语用于相似度匹配

        Args:
            text: 页面文本
            target_length: 目标短语长度（配置中段落的长度）

        Returns:
            List[str]: 短语列表
        """
        if not text:
            return []

        phrases = []

        # 清理文本，移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())

        # 1. 首先按句子分割（句号、感叹号、问号）
        sentences = re.split(r'[。！？]+', text)

        # 2. 生成与目标长度相近的连续片段
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if not sentence:
                continue

            # 添加单个句子
            phrases.append(sentence)

            # 尝试组合连续的句子，生成与目标长度相近的片段
            for j in range(i + 1, min(i + 5, len(sentences))):  # 最多组合4个连续句子
                combined_sentences = []
                for k in range(i, j + 1):
                    if sentences[k].strip():
                        combined_sentences.append(sentences[k].strip())

                if combined_sentences:
                    combined = '。'.join(combined_sentences)
                    # 只保留长度在目标长度50%-150%范围内的片段
                    if target_length * 0.5 <= len(combined) <= target_length * 1.5:
                        phrases.append(combined)

        # 3. 对于长文本，使用滑动窗口生成与目标长度相近的片段
        if len(text) > target_length:
            # 按字符长度进行滑动窗口分割
            window_sizes = [
                int(target_length * 0.8),  # 80%目标长度
                target_length,             # 100%目标长度
                int(target_length * 1.2),  # 120%目标长度
            ]

            for window_size in window_sizes:
                if window_size <= len(text):
                    step_size = max(1, window_size // 3)  # 重叠66%
                    for i in range(0, len(text) - window_size + 1, step_size):
                        phrase = text[i:i + window_size].strip()
                        if phrase and len(phrase) >= target_length * 0.5:
                            phrases.append(phrase)

        # 4. 按标点符号分割并重新组合
        parts = re.split(r'[，；、：]', text)
        for i in range(len(parts)):
            for j in range(i + 1, min(i + 6, len(parts) + 1)):  # 最多组合5个部分
                combined = ''.join(parts[i:j]).strip()
                if target_length * 0.6 <= len(combined) <= target_length * 1.4:
                    phrases.append(combined)

        return phrases

    def load_config_file(self, config_path: Union[str, Path]) -> None:
        """从配置文件加载关键词配置
        
        Args:
            config_path: 配置文件路径
        """
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise ConfigurationError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self._parse_config_data(config_data)
            self.logger.info(f"成功加载配置文件: {config_path}")
            
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise ConfigurationError(f"加载配置文件失败: {e}")
    
    def _parse_config_data(self, config_data: Dict[str, Any]) -> None:
        """解析配置数据
        
        Args:
            config_data: 配置数据字典
        """
        # 清空现有配置
        self.keyword_configs.clear()
        
        # 解析关键词配置
        keywords = config_data.get('keywords', [])
        
        for keyword_data in keywords:
            if isinstance(keyword_data, str):
                # 简单格式：只有关键词文本
                self.add_keyword(keyword_data)
            elif isinstance(keyword_data, dict):
                # 详细格式：包含所有配置
                text = keyword_data.get('text')
                if not text:
                    raise ValidationError("关键词配置缺少'text'字段")
                
                self.add_keyword(
                    text=text,
                    color=keyword_data.get('color', 'yellow'),
                    opacity=keyword_data.get('opacity', 0.5),
                    case_sensitive=keyword_data.get('case_sensitive', False),
                    regex=keyword_data.get('regex', False),
                    whole_word=keyword_data.get('whole_word', False),
                    occurrence=keyword_data.get('occurrence'),
                    pages=keyword_data.get('pages'),
                    context_phrase=keyword_data.get('context_phrase'),
                    priority=keyword_data.get('priority'),
                    id=keyword_data.get('id'),
                    similarity_threshold=keyword_data.get('similarity_threshold'),
                    annotation_style=keyword_data.get('annotation_style', 'highlight'),
                    fuzzy_enabled=keyword_data.get('fuzzy_enabled'),
                    fuzzy_start_chars=keyword_data.get('fuzzy_start_chars'),
                    fuzzy_end_chars=keyword_data.get('fuzzy_end_chars')
                )
            else:
                raise ValidationError(f"无效的关键词配置格式: {keyword_data}")
        
        # 解析全局选项
        self.global_options = config_data.get('global_options', {})

        # 检查是否有上下文降级策略配置
        if 'context_fallback_strategy' in self.global_options:
            strategy = self.global_options['context_fallback_strategy']
            if strategy in ["fallback_to_normal", "skip", "warn_only"]:
                self.context_fallback_strategy = strategy
                self.logger.info(f"设置上下文降级策略: {strategy}")
            else:
                self.logger.warning(f"无效的上下文降级策略 '{strategy}'，使用默认策略")

        # 检查是否有全局相似度阈值配置
        if 'context_similarity_threshold' in self.global_options:
            threshold = self.global_options['context_similarity_threshold']
            if isinstance(threshold, (int, float)) and 0.0 <= threshold <= 1.0:
                self.global_similarity_threshold = float(threshold)
                self.logger.info(f"设置全局相似度阈值: {self.global_similarity_threshold}")
            else:
                self.logger.warning(f"无效的相似度阈值 '{threshold}'，使用默认值 1.0")

        # 检查是否有全局模糊匹配配置
        if 'fuzzy_matching_enabled' in self.global_options:
            enabled = self.global_options['fuzzy_matching_enabled']
            if isinstance(enabled, bool):
                self.fuzzy_matching_enabled = enabled
                self.logger.info(f"设置全局模糊匹配: {'启用' if enabled else '禁用'}")
            else:
                self.logger.warning(f"无效的模糊匹配启用配置 '{enabled}'，使用默认值 False")

        if 'fuzzy_start_chars' in self.global_options:
            start_chars = self.global_options['fuzzy_start_chars']
            if isinstance(start_chars, int) and start_chars > 0:
                self.fuzzy_start_chars = start_chars
                self.logger.info(f"设置全局模糊匹配开头字符数: {start_chars}")
            else:
                self.logger.warning(f"无效的模糊匹配开头字符数 '{start_chars}'，使用默认值 2")

        if 'fuzzy_end_chars' in self.global_options:
            end_chars = self.global_options['fuzzy_end_chars']
            if isinstance(end_chars, int) and end_chars > 0:
                self.fuzzy_end_chars = end_chars
                self.logger.info(f"设置全局模糊匹配结尾字符数: {end_chars}")
            else:
                self.logger.warning(f"无效的模糊匹配结尾字符数 '{end_chars}'，使用默认值 2")

        self.logger.info(f"解析完成，共加载 {len(self.keyword_configs)} 个关键词配置")

    def _should_use_fuzzy_matching(self, config: KeywordConfig) -> bool:
        """判断是否应该对指定关键词使用模糊匹配

        Args:
            config: 关键词配置

        Returns:
            bool: 是否使用模糊匹配
        """
        # 如果关键词已经是正则表达式，不应用模糊匹配
        if config.regex:
            return False

        # 关键词级别的配置优先于全局配置
        if config.fuzzy_enabled is not None:
            return config.fuzzy_enabled

        # 使用全局配置
        return self.fuzzy_matching_enabled

    def _generate_fuzzy_regex(self, config: KeywordConfig) -> str:
        """为关键词生成模糊匹配的正则表达式

        Args:
            config: 关键词配置

        Returns:
            str: 生成的正则表达式
        """
        keyword = config.text

        # 获取有效的字符数配置
        start_chars = config.fuzzy_start_chars if config.fuzzy_start_chars is not None else self.fuzzy_start_chars
        end_chars = config.fuzzy_end_chars if config.fuzzy_end_chars is not None else self.fuzzy_end_chars

        # 验证字符数配置
        if start_chars <= 0 or end_chars <= 0:
            self.logger.warning(f"无效的模糊匹配字符数配置 (start: {start_chars}, end: {end_chars})，回退到精确匹配")
            return re.escape(keyword)

        # 如果关键词长度不足，回退到精确匹配
        if len(keyword) < start_chars + end_chars:
            self.logger.warning(f"关键词 '{keyword}' 长度({len(keyword)})不足以进行模糊匹配 (需要至少 {start_chars + end_chars} 个字符)，回退到精确匹配")
            return re.escape(keyword)

        # 检查开头和结尾字符是否重叠
        if start_chars + end_chars > len(keyword):
            self.logger.warning(f"关键词 '{keyword}' 开头({start_chars})和结尾({end_chars})字符数超过总长度({len(keyword)})，回退到精确匹配")
            return re.escape(keyword)

        # 生成模糊匹配正则表达式
        start_part = re.escape(keyword[:start_chars])
        end_part = re.escape(keyword[-end_chars:])
        # 使用 [\s\S]*? 来匹配包括换行符在内的任意字符，使用非贪婪匹配
        fuzzy_regex = f"{start_part}[\\s\\S]*?{end_part}"

        self.logger.info(f"模糊匹配详情 - 关键词: '{keyword}' (长度: {len(keyword)})")
        self.logger.info(f"  开头 {start_chars} 字符: '{keyword[:start_chars]}'")
        self.logger.info(f"  结尾 {end_chars} 字符: '{keyword[-end_chars:]}'")
        self.logger.info(f"  生成正则表达式: '{fuzzy_regex}'")
        return fuzzy_regex

    def _apply_fuzzy_matching(self, config: KeywordConfig) -> KeywordConfig:
        """应用模糊匹配，返回修改后的配置

        Args:
            config: 原始关键词配置

        Returns:
            KeywordConfig: 应用模糊匹配后的配置
        """
        if not self._should_use_fuzzy_matching(config):
            return config

        # 创建新的配置副本
        fuzzy_config = KeywordConfig(
            text=self._generate_fuzzy_regex(config),
            color=config.color,
            opacity=config.opacity,
            case_sensitive=config.case_sensitive,
            regex=True,  # 模糊匹配使用正则表达式
            whole_word=config.whole_word,
            occurrence=config.occurrence,
            pages=config.pages,
            context_phrase=config.context_phrase,
            priority=config.priority,
            id=config.id,
            similarity_threshold=config.similarity_threshold,
            annotation_style=config.annotation_style,
            fuzzy_enabled=config.fuzzy_enabled,
            fuzzy_start_chars=config.fuzzy_start_chars,
            fuzzy_end_chars=config.fuzzy_end_chars
        )

        self.logger.info(f"对关键词 '{config.text}' 应用模糊匹配，生成正则表达式: '{fuzzy_config.text}'")
        return fuzzy_config

    def _intelligent_fallback_search(self, highlighter, config: KeywordConfig,
                                   search_config: KeywordConfig, effective_pages: Optional[List[int]]) -> List[Tuple[int, List]]:
        """长关键词的智能降级搜索

        当直接模糊匹配失败时，尝试搜索关键词的各个有意义的部分

        Args:
            highlighter: PDF高亮器实例
            config: 原始关键词配置
            search_config: 应用模糊匹配后的配置
            effective_pages: 有效页面列表

        Returns:
            List[Tuple[int, List]]: 搜索结果
        """
        self.logger.info(f"开始长关键词智能降级搜索: '{config.text}'")

        # 将长关键词分解为有意义的片段
        keyword_parts = self._extract_meaningful_parts(config.text)

        all_results = []

        for part in keyword_parts:
            if len(part) < 2:  # 跳过太短的片段
                continue

            self.logger.debug(f"搜索关键词片段: '{part}'")

            try:
                # 搜索每个片段
                part_results = highlighter.search_keywords(
                    part,
                    pages=effective_pages,
                    case_sensitive=config.case_sensitive,
                    regex=False  # 片段搜索使用精确匹配
                )

                if part_results:
                    self.logger.info(f"找到关键词片段 '{part}' 的匹配")
                    all_results.extend(part_results)
                    # 找到一个匹配就足够了，避免过度匹配
                    break

            except Exception as e:
                self.logger.warning(f"搜索关键词片段 '{part}' 时出错: {e}")
                continue

        if all_results:
            self.logger.info(f"长关键词智能降级搜索成功，找到 {len(all_results)} 个匹配")
        else:
            self.logger.warning(f"长关键词智能降级搜索失败，未找到任何匹配")

        return all_results

    def _extract_meaningful_parts(self, text: str) -> List[str]:
        """从长关键词中提取有意义的部分

        Args:
            text: 长关键词文本

        Returns:
            List[str]: 有意义的片段列表，按重要性排序
        """
        parts = []

        # 1. 按标点符号分割
        punctuation_parts = re.split(r'[，。、；：！？]', text)
        for part in punctuation_parts:
            part = part.strip()
            if len(part) >= 4:  # 只保留较长的片段
                parts.append(part)

        # 2. 提取关键短语（基于常见模式）
        # 提取"XX技术"、"XX教育"、"XX学习"等模式
        patterns = [
            r'[^，。、；：！？]{2,8}技术',
            r'[^，。、；：！？]{2,8}教育',
            r'[^，。、；：！？]{2,8}学习',
            r'[^，。、；：！？]{2,8}培训',
            r'[^，。、；：！？]{2,8}教材',
            r'[^，。、；：！？]{2,8}专业'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if len(match) >= 4:
                    parts.append(match)

        # 3. 如果没有找到合适的片段，使用固定长度切分
        if not parts:
            # 从开头和结尾提取片段
            if len(text) >= 8:
                parts.append(text[:8])  # 开头8个字符
            if len(text) >= 16:
                parts.append(text[-8:])  # 结尾8个字符
            if len(text) >= 20:
                mid_start = len(text) // 2 - 4
                parts.append(text[mid_start:mid_start + 8])  # 中间8个字符

        # 去重并按长度排序（长的优先）
        unique_parts = list(set(parts))
        unique_parts.sort(key=len, reverse=True)

        self.logger.debug(f"从长关键词 '{text}' 中提取的片段: {unique_parts}")
        return unique_parts

    def get_effective_pages(self, config: KeywordConfig, method_pages: Optional[List[int]],
                           total_pages: int) -> Optional[List[int]]:
        """计算关键字的有效页面范围

        Args:
            config: 关键字配置
            method_pages: 方法参数指定的页面范围
            total_pages: PDF总页数

        Returns:
            Optional[List[int]]: 有效页面列表，None表示所有页面
        """
        # 优先级：关键字级别 > 方法参数 > 全局配置 > 所有页面
        if config.pages:
            # 关键字级别的页面配置
            return ParameterValidator.validate_pages(config.pages, total_pages)
        elif method_pages:
            # 方法参数指定的页面范围
            return method_pages
        elif self.global_options.get('pages'):
            # 全局配置的页面范围
            return ParameterValidator.validate_pages(self.global_options['pages'], total_pages)
        else:
            # 所有页面
            return None

    def search_with_context(self, highlighter, config: KeywordConfig,
                           effective_pages: Optional[List[int]]) -> List[Tuple[int, List]]:
        """使用上下文短语搜索关键词

        Args:
            highlighter: PDF高亮器实例
            config: 关键词配置
            effective_pages: 有效页面列表

        Returns:
            List[Tuple[int, List]]: 搜索结果，格式为 (页码, quads列表)
        """
        # 应用模糊匹配（如果启用）
        search_config = self._apply_fuzzy_matching(config)

        if not config.context_phrase:
            # 没有上下文短语，使用普通搜索
            self.logger.debug(f"关键词 '{config.text}' 使用普通搜索模式")

            # 对于模糊匹配的长关键词，如果直接搜索失败，尝试智能降级
            if search_config.regex and self._should_use_fuzzy_matching(config):
                # 首先尝试直接模糊匹配
                direct_results = highlighter.search_keywords(
                    search_config.text,
                    pages=effective_pages,
                    case_sensitive=search_config.case_sensitive,
                    regex=search_config.regex
                )

                if direct_results:
                    self.logger.info(f"长关键词 '{config.text}' 直接模糊匹配成功")
                    return direct_results
                else:
                    # 直接匹配失败，尝试智能降级：搜索关键词的各个部分
                    self.logger.info(f"长关键词 '{config.text}' 直接匹配失败，尝试智能降级搜索")
                    return self._intelligent_fallback_search(highlighter, config, search_config, effective_pages)
            else:
                # 普通搜索
                return highlighter.search_keywords(
                    search_config.text,
                    pages=effective_pages,
                    case_sensitive=search_config.case_sensitive,
                    regex=search_config.regex
                )

        # 验证关键词是否在上下文短语中（注意：模糊匹配时跳过此验证）
        if not search_config.regex and config.text not in config.context_phrase:
            self.logger.warning(f"关键词 '{config.text}' 不在上下文短语 '{config.context_phrase}' 中")
            return self._handle_invalid_context(highlighter, config, effective_pages)

        self.logger.debug(f"关键词 '{config.text}' 使用上下文匹配模式，上下文: '{config.context_phrase}'")

        try:
            # 获取相似度阈值
            similarity_threshold = config.similarity_threshold if config.similarity_threshold is not None else self.global_similarity_threshold

            self.logger.debug(f"使用相似度阈值: {similarity_threshold}")

            # 如果相似度阈值为1.0，使用精确匹配（向后兼容）
            if similarity_threshold >= 1.0:
                phrase_results = highlighter.search_keywords(
                    config.context_phrase,
                    pages=effective_pages,
                    case_sensitive=config.case_sensitive,
                    regex=False
                )
                if not phrase_results:
                    return self._handle_context_not_found(highlighter, config, effective_pages)
                filtered_results = []
                for page_num, phrase_quads in phrase_results:
                    if phrase_quads:
                        keyword_quads = self._extract_keyword_from_phrase(highlighter, page_num, phrase_quads, config)
                        if keyword_quads:
                            filtered_results.append((page_num, keyword_quads))
                return filtered_results if filtered_results else self._handle_context_not_found(highlighter, config, effective_pages)
            
            # --- 以下是相似度匹配的核心修复逻辑 ---
            else:
                # 1. 查找所有相似的上下文
                similar_contexts = self._find_similar_context(
                    highlighter, config.context_phrase, similarity_threshold, effective_pages
                )

                if not similar_contexts:
                    self.logger.warning(f"未找到相似度 >= {similarity_threshold} 的上下文短语 '{config.context_phrase}'")
                    return self._handle_context_not_found(highlighter, config, effective_pages)

                # 2. 【关键修复】过滤：只保留那些实际包含了目标关键词的上下文
                # 注意：对于模糊匹配，我们需要检查开头和结尾字符是否存在
                original_keyword = config.text
                if search_config.regex and self._should_use_fuzzy_matching(config):
                    # 模糊匹配：检查开头和结尾字符
                    start_chars = config.fuzzy_start_chars if config.fuzzy_start_chars is not None else self.fuzzy_start_chars
                    end_chars = config.fuzzy_end_chars if config.fuzzy_end_chars is not None else self.fuzzy_end_chars
                    start_part = original_keyword[:start_chars]
                    end_part = original_keyword[-end_chars:]

                    contexts_with_keyword = [
                        context for context in similar_contexts
                        if start_part in context[1] and end_part in context[1]
                    ]
                    self.logger.debug(f"模糊匹配验证：检查开头字符 '{start_part}' 和结尾字符 '{end_part}'")
                else:
                    # 精确匹配：检查完整关键词
                    contexts_with_keyword = [
                        context for context in similar_contexts if original_keyword in context[1]
                    ]

                # 如果过滤后列表为空，说明找到了相似的段落但它们都不含关键词
                if not contexts_with_keyword:
                    if search_config.regex and self._should_use_fuzzy_matching(config):
                        self.logger.warning(
                            f"找到了相似段落，但在这些段落中均未找到开头字符 '{start_part}' 和结尾字符 '{end_part}'"
                        )
                    else:
                        self.logger.warning(
                            f"找到了相似段落，但在这些段落中均未找到关键词 '{original_keyword}'"
                        )
                    return self._handle_context_not_found(highlighter, config, effective_pages)

                # 3. 从有效的候选列表中选择最佳相似上下文
                best_contexts = self._select_best_similar_context(contexts_with_keyword)
                
                # 记录选择的最佳相似上下文
                if best_contexts:
                    for page_num, matched_text, similarity in best_contexts:
                        self.logger.info(f"选择最佳相似上下文 (页面 {page_num}, 相似度: {similarity:.3f}): '{matched_text}'")
                else:
                    # 理论上此分支不会进入，因为前面已经判断 contexts_with_keyword 非空
                    self.logger.warning("未找到最佳相似上下文")
                    return self._handle_context_not_found(highlighter, config, effective_pages)

                # 4. 从最佳相似的上下文中搜索关键词
                # ... (后续逻辑不变)
                filtered_results = []
                processed_contexts = set()
                print(best_contexts)
                for page_num, matched_text, similarity in best_contexts:
                    context_key = (page_num, matched_text)
                    if context_key in processed_contexts:
                        continue
                    processed_contexts.add(context_key)

                    try:
                        # 使用降级搜索策略
                        keyword_quads = self._degraded_search(
                            highlighter, matched_text, page_num, search_config.text, search_config
                        )
                        if keyword_quads:
                            filtered_results.append((page_num, keyword_quads))
                    except Exception as e:
                        self.logger.warning(f"在相似上下文页面 {page_num} 搜索关键词失败: {e}")
                        continue
                
                if not filtered_results:
                    self.logger.warning(f"在相似上下文中未找到关键词 '{config.text}'")
                    return self._handle_context_not_found(highlighter, config, effective_pages)

                return filtered_results

        except Exception as e:
            self.logger.error(f"上下文搜索失败，跳过关键字 '{config.text}': {e}")
            return []
        
    def _search_keywords_in_regions(self, highlighter, keyword: str, page_num: int,
                                   context_quads: List, config: KeywordConfig) -> List:
        """在指定区域内搜索关键词 (最终修复版，兼容新旧 PyMuPDF 版本)
    
        Args:
            highlighter: PDF高亮器实例
            keyword: 要搜索的关键词
            page_num: 页码
            context_quads: 上下文区域的坐标列表
            config: 关键词配置

        Returns:
            List: 在上下文区域内找到的关键词坐标列表
        """
        # --- 版本兼容性处理 ---
        # 尝试使用新版 PyMuPDF 的命名常量
        try:
            FLAGS_DEFAULT = pymupdf.TEXT_SEARCH_FLAGS_DEFAULT
            FLAGS_IGNORE_CASE = pymupdf.TEXT_SEARCH_FLAGS_IGNORE_CASE
            FLAGS_WHOLE_WORD = pymupdf.TEXT_SEARCH_FLAGS_WHOLE_WORD
        # 如果失败（AttributeError），则回退到旧版 PyMuPDF 的整数值
        except AttributeError:
            self.logger.debug("PyMuPDF 版本较旧，回退到使用整数 flags 进行搜索。")
            FLAGS_DEFAULT = 0  # 默认
            FLAGS_IGNORE_CASE = 1  # 忽略大小写
            FLAGS_WHOLE_WORD = 2  # 全词匹配

        # 如果是正则表达式，由于 PyMuPDF 的 search_for 不支持在 clip 区域内使用正则，
        # 我们回退到原有但可能不太精确的方法：全页搜索后判断交集。
        if config.regex:
            self.logger.warning("在指定区域内进行正则表达式搜索可能不精确，采用全页扫描后判断交集的方式。")
            keyword_quads = []
            try:
                page = highlighter.doc[page_num - 1]
                page_keyword_results = highlighter.search_keywords(
                    keyword,
                    pages=[page_num],
                    case_sensitive=config.case_sensitive,
                    regex=config.regex
                )

                # 过滤出在上下文区域内的关键词
                # 修改：每个段落只标记第一个关键词实例
                processed_contexts = set()  # 跟踪已处理的上下文区域
                for _, page_keyword_quads in page_keyword_results:
                    for kw_quad in page_keyword_quads:
                        for context_quad in context_quads:
                            # 检查这个上下文区域是否已经处理过
                            context_key = id(context_quad)
                            if context_key in processed_contexts:
                                continue
                                
                            if context_quad.rect.intersects(kw_quad.rect):
                                keyword_quads.append(kw_quad)
                                processed_contexts.add(context_key)  # 标记这个上下文区域已处理
                                break
            except Exception as e:
                self.logger.warning(f"在区域内搜索正则表达式失败 (页面 {page_num}): {e}")
            return keyword_quads

        # 对于普通文本，使用更精确、更可靠的 clip 区域搜索
        all_keyword_quads = []
        try:
            page = highlighter.doc[page_num - 1]

            # 根据配置设置 PyMuPDF 的搜索标志
            search_flags = FLAGS_DEFAULT
            if not config.case_sensitive:
                search_flags |= FLAGS_IGNORE_CASE
            if config.whole_word:
                search_flags |= FLAGS_WHOLE_WORD

            # 遍历每个上下文区域，并在该区域内进行精确搜索
            # 修改：每个段落只标记第一个关键词实例
            for context_quad in context_quads:
                found_quads = page.search_for(
                    keyword,
                    clip=context_quad.rect,
                    flags=search_flags,
                    quads=True  # 确保返回Quad对象
                )

                if found_quads:
                    self.logger.debug(
                        f"在页面 {page_num} 的上下文区域 {context_quad.rect} 内找到关键词 '{keyword}'"
                    )
                    # 只取第一个匹配的实例，而不是所有实例
                    all_keyword_quads.append(found_quads[0])

        except Exception as e:
            self.logger.warning(
                f"在区域内精确搜索关键词 '{keyword}' 失败 (页面 {page_num}): {e}"
            )

        return all_keyword_quads

    def _degraded_search(self, highlighter, matched_text: str, page_num: int,
                        keyword: str, config: KeywordConfig) -> List:
        """降级搜索策略：当完整文本搜索失败时，尝试截取文本进行搜索
        
        Args:
            highlighter: PDF高亮器实例
            matched_text: 要搜索的文本片段
            page_num: 页码
            keyword: 目标关键词
            config: 关键词配置
            
        Returns:
            List: 搜索到的关键词坐标列表
        """
        self.logger.info(f"开始降级搜索策略，页面 {page_num}，完整文本: '{matched_text}'")
        
        # 1. 首先尝试使用完整文本搜索
        context_results = highlighter.search_keywords(
            matched_text,
            pages=[page_num],
            case_sensitive=False,
            regex=False
        )
        
        # 如果完整文本搜索成功，直接返回结果
        if context_results:
            for ctx_page_num, context_quads in context_results:
                if context_quads:
                    keyword_quads = self._search_keywords_in_regions(
                        highlighter, keyword, page_num, context_quads, config
                    )
                    if keyword_quads:
                        self.logger.info(f"完整文本搜索成功，找到关键词 '{keyword}'")
                        return keyword_quads
        
        # 2. 如果完整文本搜索失败，实施降级搜索策略
        self.logger.info(f"完整文本搜索失败，开始实施降级搜索策略")
        
        # 获取文本片段的智能截取版本
        text_fragments = self._generate_text_fragments(matched_text, keyword)
        
        # 尝试不同长度的文本片段进行搜索
        for i, fragment in enumerate(text_fragments):
            self.logger.debug(f"尝试降级搜索片段 {i+1}/{len(text_fragments)}: '{fragment}'")
            
            try:
                fragment_results = highlighter.search_keywords(
                    fragment,
                    pages=[page_num],
                    case_sensitive=False,
                    regex=False
                )
                
                if fragment_results:
                    for ctx_page_num, context_quads in fragment_results:
                        if context_quads:
                            keyword_quads = self._search_keywords_in_regions(
                                highlighter, keyword, page_num, context_quads, config
                            )
                            if keyword_quads:
                                self.logger.info(f"降级搜索成功，在片段 '{fragment}' 中找到关键词 '{keyword}'")
                                return keyword_quads
            except Exception as e:
                self.logger.warning(f"降级搜索片段 '{fragment}' 时出错: {e}")
                continue
        
        self.logger.warning(f"降级搜索策略失败，未能在页面 {page_num} 找到关键词 '{keyword}'")
        return []

    def _generate_text_fragments(self, text: str, keyword: str) -> List[str]:
        """生成文本片段用于降级搜索，确保关键词完整性
        
        Args:
            text: 原始文本
            keyword: 目标关键词
            
        Returns:
            List[str]: 文本片段列表，按优先级排序
        """
        fragments = []
        
        # 确保关键词在文本中
        if keyword not in text:
            self.logger.warning(f"关键词 '{keyword}' 不在文本 '{text}' 中")
            return fragments
        
        # 1. 首先尝试关键词周围的固定长度文本
        keyword_pos = text.find(keyword)
        if keyword_pos == -1:
            return fragments
            
        # 定义不同的截取策略
        strategies = [
            # 策略1: 关键词前后各保留一定字符数
            lambda: self._extract_with_context(text, keyword, keyword_pos, 50, 50),
            # 策略2: 关键词前后各保留较少字符数
            lambda: self._extract_with_context(text, keyword, keyword_pos, 20, 20),
            # 策略3: 关键词前后各保留更多字符数
            lambda: self._extract_with_context(text, keyword, keyword_pos, 100, 100),
            # 策略4: 仅保留关键词及前后少量字符
            lambda: self._extract_with_context(text, keyword, keyword_pos, 5, 5),
            # 策略5: 保留关键词到行首行尾
            lambda: self._extract_to_line_boundaries(text, keyword, keyword_pos),
            # 策略6: 保留关键词到句首句尾
            lambda: self._extract_to_sentence_boundaries(text, keyword, keyword_pos)
        ]
        
        # 应用各种策略生成片段
        for strategy in strategies:
            try:
                fragment = strategy()
                if fragment and fragment not in fragments:
                    fragments.append(fragment)
            except Exception as e:
                self.logger.warning(f"文本片段生成策略失败: {e}")
                continue
        
        # 如果没有生成有效的片段，至少返回关键词本身
        if not fragments:
            fragments.append(keyword)
            
        self.logger.debug(f"生成了 {len(fragments)} 个文本片段用于降级搜索")
        return fragments

    def _extract_with_context(self, text: str, keyword: str, keyword_pos: int,
                             before_chars: int, after_chars: int) -> str:
        """提取关键词前后指定字符数的文本片段
        
        Args:
            text: 原始文本
            keyword: 目标关键词
            keyword_pos: 关键词在文本中的位置
            before_chars: 关键词前保留的字符数
            after_chars: 关键词后保留的字符数
            
        Returns:
            str: 提取的文本片段
        """
        start_pos = max(0, keyword_pos - before_chars)
        end_pos = min(len(text), keyword_pos + len(keyword) + after_chars)
        return text[start_pos:end_pos].strip()

    def _extract_to_line_boundaries(self, text: str, keyword: str, keyword_pos: int) -> str:
        """提取到行边界的文本片段
        
        Args:
            text: 原始文本
            keyword: 目标关键词
            keyword_pos: 关键词在文本中的位置
            
        Returns:
            str: 提取的文本片段
        """
        # 查找行首（换行符或文本开始）
        line_start = text.rfind('\n', 0, keyword_pos)
        if line_start == -1:
            line_start = 0
        else:
            line_start += 1  # 跳过换行符
            
        # 查找行尾（换行符或文本结束）
        line_end = text.find('\n', keyword_pos + len(keyword))
        if line_end == -1:
            line_end = len(text)
            
        return text[line_start:line_end].strip()

    def _extract_to_sentence_boundaries(self, text: str, keyword: str, keyword_pos: int) -> str:
        """提取到句子边界的文本片段
        
        Args:
            text: 原始文本
            keyword: 目标关键词
            keyword_pos: 关键词在文本中的位置
            
        Returns:
            str: 提取的文本片段
        """
        # 定义句子分隔符
        sentence_separators = '.!?。！？'
        
        # 查找句子开始（句子分隔符或文本开始）
        sentence_start = 0
        for i in range(keyword_pos - 1, -1, -1):
            if text[i] in sentence_separators:
                sentence_start = i + 1
                break
                
        # 查找句子结束（句子分隔符或文本结束）
        sentence_end = len(text)
        for i in range(keyword_pos + len(keyword), len(text)):
            if text[i] in sentence_separators:
                sentence_end = i + 1
                break
                
        return text[sentence_start:sentence_end].strip()

    def _extract_keyword_from_phrase(self, highlighter, page_num: int,
                                   phrase_quads: List, config: KeywordConfig) -> List:
        """从上下文短语中提取目标关键词的坐标

        Args:
            highlighter: PDF高亮器实例
            page_num: 页码
            phrase_quads: 上下文短语的坐标列表
            config: 关键词配置

        Returns:
            List: 目标关键词的坐标列表
        """
        keyword_quads = []

        try:
            page = highlighter.doc[page_num - 1]  # 转换为0基索引
        except (IndexError, AttributeError) as e:
            self.logger.error(f"无法访问页面 {page_num}: {e}")
            return []

        for phrase_quad in phrase_quads:
            try:
                # 获取短语的文本内容
                phrase_text = page.get_textbox(phrase_quad.rect)

                if not phrase_text or not phrase_text.strip():
                    self.logger.debug(f"页面 {page_num} 的短语区域为空")
                    continue

                # 检查关键词是否在短语中
                if config.text not in phrase_text:
                    self.logger.debug(f"关键词 '{config.text}' 不在短语文本 '{phrase_text}' 中")
                    continue

                # 计算关键词在短语中的位置
                keyword_start = phrase_text.find(config.text)
                if keyword_start == -1:
                    continue

                # 验证短语长度
                phrase_length = len(phrase_text)
                if phrase_length == 0:
                    self.logger.warning(f"短语长度为0，跳过处理")
                    continue

                keyword_length = len(config.text)

                # 计算关键词的相对位置比例
                start_ratio = keyword_start / phrase_length
                end_ratio = (keyword_start + keyword_length) / phrase_length

                # 验证比例范围
                if not (0 <= start_ratio <= 1 and 0 <= end_ratio <= 1):
                    self.logger.warning(f"计算的位置比例超出范围: start={start_ratio}, end={end_ratio}")
                    continue

                # 计算关键词的实际坐标
                rect = phrase_quad.rect
                width = rect.width

                if width <= 0:
                    self.logger.warning(f"短语区域宽度无效: {width}")
                    continue

                # 简化处理：假设文本是水平排列的
                keyword_x0 = rect.x0 + width * start_ratio
                keyword_x1 = rect.x0 + width * end_ratio

                # 验证坐标
                if keyword_x0 >= keyword_x1:
                    self.logger.warning(f"关键词坐标无效: x0={keyword_x0}, x1={keyword_x1}")
                    continue

                # 创建关键词的Quad
                keyword_quad = pymupdf.Quad(
                    pymupdf.Point(keyword_x0, rect.y0),
                    pymupdf.Point(keyword_x1, rect.y0),
                    pymupdf.Point(keyword_x0, rect.y1),
                    pymupdf.Point(keyword_x1, rect.y1)
                )

                keyword_quads.append(keyword_quad)
                self.logger.debug(f"成功提取关键词 '{config.text}' 的坐标")

            except Exception as e:
                self.logger.warning(f"提取关键词坐标失败 (页面 {page_num}): {e}")
                continue

        return keyword_quads

    def _handle_invalid_context(self, highlighter, config: KeywordConfig,
                               effective_pages: Optional[List[int]]) -> List[Tuple[int, List]]:
        """处理无效的上下文配置（关键字不在上下文短语中）

        Args:
            highlighter: PDF高亮器实例
            config: 关键词配置
            effective_pages: 有效页面列表

        Returns:
            List[Tuple[int, List]]: 搜索结果，格式为 (页码, quads列表)
        """
        if self.context_fallback_strategy == "fallback_to_normal":
            self.logger.warning(f"关键词 '{config.text}' 降级为普通关键字搜索")
            return highlighter.search_keywords(
                config.text,
                pages=effective_pages,
                case_sensitive=config.case_sensitive,
                regex=config.regex
            )
        elif self.context_fallback_strategy == "skip":
            self.logger.warning(f"跳过关键字 '{config.text}'（无效的上下文配置）")
            return []
        elif self.context_fallback_strategy == "warn_only":
            self.logger.warning(f"关键字 '{config.text}' 不会被高亮（无效的上下文配置）")
            return []
        else:
            # 默认降级策略
            self.logger.warning(f"使用默认策略：关键词 '{config.text}' 降级为普通搜索")
            return highlighter.search_keywords(
                config.text,
                pages=effective_pages,
                case_sensitive=config.case_sensitive,
                regex=config.regex
            )

    def _handle_context_not_found(self, highlighter, config: KeywordConfig,
                                 effective_pages: Optional[List[int]]) -> List[Tuple[int, List]]:
        """处理上下文短语不存在的情况

        Args:
            highlighter: PDF高亮器实例
            config: 关键词配置
            effective_pages: 有效页面列表

        Returns:
            List[Tuple[int, List]]: 搜索结果，格式为 (页码, quads列表)
        """
        # 严格模式：不降级为普通搜索，只在相似段落中查找关键词
        self.logger.warning(f"未找到相似度足够的上下文段落 '{config.context_phrase}'，跳过关键字 '{config.text}'")
        return []

    def collect_all_highlights(self, highlighter, effective_pages_map: Dict[str, Optional[List[int]]]) -> List[HighlightRegion]:
        """收集所有关键词的高亮区域

        Args:
            highlighter: PDF高亮器实例
            effective_pages_map: 关键词到有效页面的映射

        Returns:
            List[HighlightRegion]: 高亮区域列表
        """
        all_regions = []

        for config in self.keyword_configs:
            effective_pages = effective_pages_map.get(config.text)

            # 搜索关键词
            search_results = self.search_with_context(highlighter, config, effective_pages)

            # 转换为高亮区域
            for page_num, quads in search_results:
                for quad in quads:
                    region = HighlightRegion(
                        page_num=page_num,
                        quad=quad,
                        keyword=config.text,
                        keyword_id=config.id,
                        color=config.color,
                        opacity=config.opacity,
                        priority=config.priority,
                        annotation_style=config.annotation_style
                    )
                    all_regions.append(region)

        return all_regions

    def filter_overlapping_regions(self, regions: List[HighlightRegion]) -> List[HighlightRegion]:
        """过滤重叠的高亮区域，保留高优先级的

        Args:
            regions: 所有高亮区域列表

        Returns:
            List[HighlightRegion]: 过滤后的高亮区域列表
        """
        # 按优先级排序（数字越小优先级越高）
        sorted_regions = sorted(regions, key=lambda r: r.priority)

        filtered_regions = []

        for region in sorted_regions:
            # 检查是否与已选择的区域重叠
            has_overlap = False
            for existing_region in filtered_regions:
                if region.overlaps_with(existing_region):
                    has_overlap = True
                    self.logger.debug(f"跳过重叠区域: '{region.keyword}' (优先级: {region.priority}) "
                                    f"与 '{existing_region.keyword}' (优先级: {existing_region.priority}) 重叠")
                    break

            if not has_overlap:
                filtered_regions.append(region)

        return filtered_regions

    def apply_highlights(self, highlighter, regions: List[HighlightRegion]) -> Dict[str, Dict[str, Any]]:
        """应用高亮到PDF

        Args:
            highlighter: PDF高亮器实例
            regions: 要应用的高亮区域列表

        Returns:
            Dict: 按关键词ID分组的处理统计信息
        """
        keyword_stats = {}

        # 按关键词ID分组统计
        for region in regions:
            if region.keyword_id not in keyword_stats:
                keyword_stats[region.keyword_id] = {
                    'id': region.keyword_id,
                    'text': region.keyword,
                    'matches_found': 0,
                    'highlights_added': 0,
                    'color': region.color,
                    'opacity': region.opacity,
                    'annotation_style': region.annotation_style,
                    'status': 'pending',
                    'pages': set()  # 使用set来避免重复页面
                }
            keyword_stats[region.keyword_id]['matches_found'] += 1
            keyword_stats[region.keyword_id]['pages'].add(region.page_num)

        # 应用高亮
        for region in regions:
            page = highlighter.doc[region.page_num - 1]  # 转换为0基索引

            try:
                # 根据不同的标记样式应用相应的高亮逻辑
                if region.annotation_style == "highlight":
                    # 添加高亮注释
                    annot = page.add_highlight_annot(region.quad)
                elif region.annotation_style == "underline":
                    # 添加下划线注释
                    annot = page.add_underline_annot(region.quad)
                elif region.annotation_style == "strikeout":
                    # 添加删除线注释
                    annot = page.add_strikeout_annot(region.quad)
                elif region.annotation_style == "squiggly":
                    # 添加波浪线注释
                    annot = page.add_squiggly_annot(region.quad)
                else:
                    # 默认使用高亮注释
                    self.logger.warning(f"未知的标记样式 '{region.annotation_style}'，使用默认高亮样式")
                    annot = page.add_highlight_annot(region.quad)

                # 设置注释属性
                annot.set_colors(stroke=region.color)
                annot.set_opacity(region.opacity)
                annot.update()

                # 记录成功应用的高亮
                region.applied = True
                keyword_stats[region.keyword_id]['highlights_added'] += 1
                keyword_stats[region.keyword_id]['status'] = 'success'

            except Exception as e:
                self.logger.warning(f"添加高亮失败: {e}")
                keyword_stats[region.keyword_id]['status'] = 'failed'

        # 将页面集合转换为排序的列表
        for stats in keyword_stats.values():
            stats['pages'] = sorted(list(stats['pages']))

        return keyword_stats

    def _generate_complete_stats(self, processed_stats: Dict[str, Dict[str, Any]],
                                effective_pages_map: Dict[str, Optional[List[int]]] = None) -> Dict[str, Dict[str, Any]]:
        """生成完整的关键词统计信息，包括未找到匹配的关键词

        Args:
            processed_stats: 已处理的关键词统计信息
            effective_pages_map: 关键词到有效页面的映射

        Returns:
            Dict: 完整的关键词统计信息
        """
        complete_stats = {}

        # 添加所有配置的关键词
        for config in self.keyword_configs:
            if config.id in processed_stats:
                # 已处理的关键词
                stats = processed_stats[config.id].copy()

                # 添加配置信息
                stats.update({
                    'pages': config.pages,
                    'context_phrase': config.context_phrase,
                    'priority': config.priority,
                    'case_sensitive': config.case_sensitive,
                    'regex': config.regex,
                    'whole_word': config.whole_word,
                    'annotation_style': config.annotation_style
                })

                complete_stats[config.id] = stats
            else:
                # 未找到匹配的关键词，添加尝试搜索的页面信息
                attempted_pages = []
                if effective_pages_map and config.text in effective_pages_map:
                    attempted_pages = effective_pages_map[config.text] or []

                complete_stats[config.id] = {
                    'id': config.id,
                    'text': config.text,
                    'matches_found': 0,
                    'highlights_added': 0,
                    'color': config.color,
                    'opacity': config.opacity,
                    'annotation_style': config.annotation_style,
                    'status': 'no_matches',
                    'pages': attempted_pages,  # 记录尝试搜索的页面
                    'context_phrase': config.context_phrase,
                    'priority': config.priority,
                    'case_sensitive': config.case_sensitive,
                    'regex': config.regex,
                    'whole_word': config.whole_word
                }

        return complete_stats

    def _log_processing_results(self, all_stats: Dict[str, Dict[str, Any]]) -> None:
        """记录简化的处理结果

        Args:
            all_stats: 完整的关键词统计信息
        """
        self.logger.info("=== 关键词处理结果统计 ===")

        successful_keywords = []
        failed_keywords = []

        for keyword_id, stats in all_stats.items():
            if stats['highlights_added'] > 0:
                successful_keywords.append(stats)
                self.logger.info(f"✅ [{stats['id']}] '{stats['text']}': 添加 {stats['highlights_added']} 个高亮")
            else:
                failed_keywords.append(stats)
                self.logger.warning(f"❌ [{stats['id']}] '{stats['text']}': 未被高亮")

        # 输出统计摘要
        total = len(all_stats)
        successful = len(successful_keywords)
        failed = len(failed_keywords)

        self.logger.info(f"📊 处理摘要: 总计 {total} 个关键词")
        self.logger.info(f"   ✅ 成功高亮: {successful} 个")
        self.logger.info(f"   ❌ 未被高亮: {failed} 个")

        if successful_keywords:
            total_highlights = sum(kw['highlights_added'] for kw in successful_keywords)
            self.logger.info(f"🎯 总共添加 {total_highlights} 个高亮")

    def process(self, output_path: Optional[str] = None,
               pages: Optional[List[int]] = None) -> Dict[str, Any]:
        """处理PDF文件，应用所有关键词高亮
        
        Args:
            output_path: 输出文件路径
            pages: 指定处理的页码列表
            
        Returns:
            Dict: 处理结果
        """
        if not self.keyword_configs:
            raise ValidationError("没有配置任何关键词")
        
        # 生成输出路径
        if not output_path:
            input_path = Path(self.pdf_path)
            suffix = self.global_options.get('output_suffix', '_highlighted')
            output_path = input_path.parent / f"{input_path.stem}{suffix}{input_path.suffix}"
        
        output_path = ParameterValidator.validate_output_path(str(output_path), self.pdf_path)
        
        result = {
            'input_file': self.pdf_path,
            'output_file': output_path,
            'keywords_processed': {},
            'keywords_by_id': {},
            'summary': {
                'total_keywords': len(self.keyword_configs),
                'successful_keywords': 0,
                'failed_keywords': 0,
                'skipped_keywords': 0
            },
            'total_highlights': 0,
            'success': False,
            'error': None
        }
        
        try:
            with PDFHighlighter(self.pdf_path) as highlighter:
                total_pages = highlighter.get_page_count()

                # 计算每个关键词的有效页面范围
                effective_pages_map = {}
                for config in self.keyword_configs:
                    effective_pages = self.get_effective_pages(config, pages, total_pages)
                    effective_pages_map[config.text] = effective_pages

                self.logger.info(f"开始收集所有关键词的匹配结果...")

                # 收集所有高亮区域
                all_regions = self.collect_all_highlights(highlighter, effective_pages_map)

                if not all_regions:
                    self.logger.warning("未找到任何匹配项，不生成输出文件")
                    result['error'] = "未找到任何匹配项"
                    # 仍然生成完整的统计信息，包括未找到匹配的关键词
                    all_keyword_stats = self._generate_complete_stats({}, effective_pages_map)
                    result['keywords_by_id'] = all_keyword_stats
                    result['keywords_processed'] = {stats['text']: stats for stats in all_keyword_stats.values()}

                    # 按匹配状态归类关键词（全部失败）
                    result['keywords_by_status'] = {
                        'successful': {},
                        'failed': all_keyword_stats
                    }
                    return result

                self.logger.info(f"收集到 {len(all_regions)} 个高亮区域")

                # 过滤重叠区域
                filtered_regions = self.filter_overlapping_regions(all_regions)

                self.logger.info(f"过滤后剩余 {len(filtered_regions)} 个高亮区域")

                # 应用高亮
                keyword_stats = self.apply_highlights(highlighter, filtered_regions)

                # 处理未找到匹配的关键词
                all_keyword_stats = self._generate_complete_stats(keyword_stats, effective_pages_map)

                # 更新结果统计
                total_highlights = sum(stats['highlights_added'] for stats in all_keyword_stats.values())
                result['keywords_by_id'] = all_keyword_stats
                result['keywords_processed'] = {stats['text']: stats for stats in all_keyword_stats.values()}

                # 按匹配状态归类关键词
                successful_keywords = {}
                failed_keywords = {}

                for keyword_id, stats in all_keyword_stats.items():
                    if stats['highlights_added'] > 0:
                        successful_keywords[keyword_id] = stats
                    else:
                        failed_keywords[keyword_id] = stats

                result['keywords_by_status'] = {
                    'successful': successful_keywords,
                    'failed': failed_keywords
                }

                # 统计摘要
                successful_count = len(successful_keywords)
                failed_count = len(failed_keywords)
                skipped_count = sum(1 for stats in all_keyword_stats.values() if stats['status'] == 'skipped')

                result['summary'].update({
                    'successful_keywords': successful_count,
                    'failed_keywords': failed_count,
                    'skipped_keywords': skipped_count
                })

                # 记录简化的处理结果
                self._log_processing_results(all_keyword_stats)
                
                # 保存文件
                if total_highlights > 0:
                    highlighter.save_pdf(output_path)
                    result['total_highlights'] = total_highlights
                    result['success'] = True
                    self.logger.info(f"处理完成，总共添加 {total_highlights} 个高亮")
                    self.logger.info(f"输出文件: {output_path}")
                else:
                    result['error'] = "未找到任何匹配项"
                    self.logger.warning("未找到任何匹配项，不生成输出文件")
        
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"处理失败: {e}")
        
        return result
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要
        
        Returns:
            Dict: 配置摘要信息
        """
        return {
            'pdf_file': self.pdf_path,
            'keyword_count': len(self.keyword_configs),
            'keywords': [
                {
                    'text': config.text,
                    'color': config.color,
                    'opacity': config.opacity,
                    'annotation_style': config.annotation_style,
                    'options': {
                        'case_sensitive': config.case_sensitive,
                        'regex': config.regex,
                        'whole_word': config.whole_word,
                        'occurrence': config.occurrence
                    }
                }
                for config in self.keyword_configs
            ],
            'global_options': self.global_options
        }


def create_sample_multi_config(output_path: Union[str, Path]) -> Path:
    """创建示例多关键词配置文件
    
    Args:
        output_path: 输出文件路径
        
    Returns:
        Path: 创建的配置文件路径
    """
    sample_config = {
        "keywords": [
            {
                "id": "frontend_dev",
                "text": "前端开发",
                "color": "yellow",
                "opacity": 0.6,
                "case_sensitive": False,
                "regex": False,
                "pages": "1-5",
                "annotation_style": "highlight"
            },
            {
                "id": "backend_dev",
                "text": "后端开发",
                "color": "blue",
                "opacity": 0.7,
                "case_sensitive": False,
                "regex": False,
                "pages": "6-10",
                "annotation_style": "underline"
            },
            {
                "id": "fullstack_engineer",
                "text": "全栈工程师",
                "color": "green",
                "opacity": 0.5,
                "case_sensitive": False,
                "regex": False,
                "annotation_style": "squiggly"
            },
            {
                "id": "programming_languages",
                "text": "Python|Java|JavaScript",
                "color": "orange",
                "opacity": 0.8,
                "case_sensitive": False,
                "regex": True,
                "pages": "1,3,5-7",
                "annotation_style": "highlight"
            },
            {
                "id": "api_keyword",
                "text": "API",
                "color": "#FF6B6B",
                "opacity": 0.6,
                "case_sensitive": True,
                "regex": False,
                "whole_word": True,
                "pages": "2,4,8-10",
                "annotation_style": "strikeout"
            },
            {
                "id": "important_concept_early",
                "text": "重要概念",
                "color": "red",
                "opacity": 0.7,
                "pages": "1-3",
                "annotation_style": "highlight"
            },
            {
                "id": "important_concept_late",
                "text": "重要概念",
                "color": "purple",
                "opacity": 0.7,
                "pages": "8-10",
                "annotation_style": "underline"
            },
            {
                "id": "review_context",
                "text": "审核",
                "color": "red",
                "opacity": 0.8,
                "pages": "3",
                "context_phrase": "添加以供审核",
                "annotation_style": "squiggly"
            },
            {
                "id": "confirm_context",
                "text": "确认",
                "color": "blue",
                "opacity": 0.7,
                "context_phrase": "请确认无误后提交",
                "annotation_style": "strikeout"
            }
        ],
        "global_options": {
            "pages": None,
            "occurrence": None,
            "whole_word": False,
            "overwrite": False,
            "output_suffix": "_multi_highlighted"
        },
        "processing": {
            "max_workers": 4,
            "recursive": True,
            "skip_existing": True
        },
        "logging": {
            "level": "INFO",
            "file": "multi_highlight.log",
            "console": True
        }
    }
    
    output_path = Path(output_path)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)
    
    return output_path
