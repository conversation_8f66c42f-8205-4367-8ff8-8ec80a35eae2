{"description": "上下文短语不存在时的降级策略配置示例", "keywords": [{"comment": "正常配置 - 上下文短语存在", "text": "应用", "color": "green", "context_phrase": "苹果ipa应用", "priority": 1}, {"comment": "错误配置 - 上下文短语在PDF中不存在", "text": "审核", "color": "red", "context_phrase": "这个上下文短语在PDF中不存在包含审核", "priority": 1}, {"comment": "错误配置 - 另一个不存在的上下文短语", "text": "提交", "color": "blue", "context_phrase": "另一个不存在的上下文短语包含提交", "priority": 1}, {"comment": "正常配置 - 无上下文限制", "text": "普通关键词", "color": "yellow", "priority": 2}], "global_options": {"context_fallback_strategy": "fallback_to_normal", "output_suffix": "_context_fallback_demo"}, "fallback_strategies": {"fallback_to_normal": {"description": "当上下文短语不存在时，降级为普通关键字搜索（推荐）", "use_case": "希望在配置错误时仍能获得搜索结果", "result": "不存在的上下文短语会被忽略，关键字按普通模式搜索"}, "skip": {"description": "当上下文短语不存在时，完全跳过该关键字", "use_case": "只想处理配置正确的关键字，忽略错误配置", "result": "不存在的上下文短语对应的关键字不会被处理"}, "warn_only": {"description": "当上下文短语不存在时，仅记录警告，不进行搜索", "use_case": "严格模式，只处理完全正确的配置", "result": "不存在的上下文短语对应的关键字不会被处理，但会记录详细警告"}}, "usage_examples": {"config_file": {"description": "在配置文件中设置降级策略", "example": {"global_options": {"context_fallback_strategy": "fallback_to_normal"}}}, "programming_api": {"description": "在编程API中设置降级策略", "example": "highlighter = MultiKeywordHighlighter('file.pdf', context_fallback_strategy='skip')"}}, "error_scenarios": [{"scenario": "上下文短语在PDF中不存在", "example": "context_phrase: '这个短语不在PDF中'", "handling": "根据降级策略处理：降级搜索/跳过/仅警告"}, {"scenario": "关键字不在上下文短语中", "example": "text: '审核', context_phrase: '添加以供1审2核'", "handling": "在配置阶段记录警告，在搜索阶段降级处理"}, {"scenario": "空的上下文短语", "example": "context_phrase: ''", "handling": "忽略上下文配置，按普通关键字处理"}]}