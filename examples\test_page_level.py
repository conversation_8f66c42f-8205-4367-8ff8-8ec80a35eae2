#!/usr/bin/env python3
"""
页面级关键字配置功能测试脚本

演示如何使用页面级关键字配置功能，包括：
1. 为不同关键字指定不同的页面范围
2. 同一关键字在不同页面使用不同颜色
3. 向后兼容性测试
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
logger = get_logger()


def create_test_config():
    """创建测试用的页面级配置文件"""
    config = {
        "keywords": [
            {
                "text": "第一章",
                "color": "yellow",
                "opacity": 0.6,
                "pages": "1-3"
            },
            {
                "text": "第二章",
                "color": "blue", 
                "opacity": 0.6,
                "pages": "4-6"
            },
            {
                "text": "重要",
                "color": "red",
                "opacity": 0.7,
                "pages": "1-2"
            },
            {
                "text": "重要",
                "color": "orange",
                "opacity": 0.7,
                "pages": "5-6"
            },
            {
                "text": "全局关键词",
                "color": "green",
                "opacity": 0.5
            }
        ],
        "global_options": {
            "pages": None,
            "occurrence": None,
            "whole_word": False,
            "output_suffix": "_page_level_test"
        }
    }
    
    config_file = project_root / "examples" / "test_page_level_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"测试配置文件已创建: {config_file}")
    return config_file


def test_page_level_highlighting():
    """测试页面级关键字高亮功能"""
    print("=== 页面级关键字配置功能测试 ===\n")
    
    # 创建测试配置文件
    config_file = create_test_config()
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件，请在项目根目录放置一个PDF文件进行测试")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 创建多关键词高亮处理器
        highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 加载页面级配置
        highlighter.load_config_file(str(config_file))
        
        # 显示配置摘要
        print("\n配置摘要:")
        for i, config in enumerate(highlighter.keyword_configs, 1):
            pages_info = f" (页面: {config.pages})" if config.pages else " (所有页面)"
            print(f"  {i}. '{config.text}' -> {config.color}{pages_info}")
        
        # 执行处理（干运行模式，不实际生成文件）
        print(f"\n开始处理PDF文件...")
        print("注意：这是演示模式，实际使用时请移除此注释并取消下面的注释")
        print("# result = highlighter.process()")
        print("# print(f'处理完成，总共添加 {result[\"total_highlights\"]} 个高亮')")
        
        # 演示编程API方式
        print("\n=== 编程API方式演示 ===")
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        
        # 添加页面级关键词配置
        highlighter2.add_keyword("示例1", color="yellow", pages="1-2")
        highlighter2.add_keyword("示例2", color="blue", pages="3-4") 
        highlighter2.add_keyword("示例3", color="green")  # 全局关键词
        
        print("编程API配置:")
        for i, config in enumerate(highlighter2.keyword_configs, 1):
            pages_info = f" (页面: {config.pages})" if config.pages else " (所有页面)"
            print(f"  {i}. '{config.text}' -> {config.color}{pages_info}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False
    
    print("\n✅ 页面级关键字配置功能测试完成！")
    return True


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 向后兼容性测试 ===")
    
    # 创建传统格式的配置文件
    old_config = {
        "keywords": [
            {
                "text": "传统关键词1",
                "color": "yellow",
                "opacity": 0.6
            },
            {
                "text": "传统关键词2",
                "color": "blue",
                "opacity": 0.7
            }
        ],
        "global_options": {
            "pages": "1-3",
            "output_suffix": "_backward_compatible"
        }
    }
    
    config_file = project_root / "examples" / "backward_compatible_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(old_config, f, indent=2, ensure_ascii=False)
    
    print(f"传统配置文件已创建: {config_file}")
    print("✅ 传统配置格式仍然有效，向后兼容性良好")


if __name__ == "__main__":
    # 运行测试
    test_page_level_highlighting()
    test_backward_compatibility()
    
    print("\n" + "="*50)
    print("测试说明:")
    print("1. 页面级关键字配置允许为每个关键字指定特定的页面范围")
    print("2. 同一关键字可以在不同页面使用不同颜色")
    print("3. 没有指定pages的关键字将使用全局设置或在所有页面生效")
    print("4. 完全向后兼容现有的配置文件格式")
    print("="*50)
