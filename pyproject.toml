[project]
name = "pdf-highlighter"
version = "0.1.0"
description = "A PDF keyword highlighting tool based on PyMuPDF that supports multi-format PDF files with advanced search and highlighting features"
readme = "README.md"
license = { text = "MIT" }
authors = [
    { name = "jiwei", email = "<EMAIL>" }
]
maintainers = [
    { name = "jiwei", email = "<EMAIL>" }
]
requires-python = ">=3.8.1"
dependencies = [
    "pillow>=10.4.0",
    "pymupdf>=1.24.11,<2.0.0",
    "pytesseract>=0.3.13",
]
keywords = ["pdf", "highlight", "pymupdf", "text-search", "annotation", "document-processing", "cli-tool"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: End Users/Desktop",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business",
    "Topic :: Text Processing",
    "Topic :: Utilities",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Environment :: Console",
    "Natural Language :: Chinese (Simplified)",
    "Natural Language :: English",
]

[project.urls]
Homepage = "https://github.com/jiwei/pdf-highlighter"
Documentation = "https://github.com/jiwei/pdf-highlighter#readme"
Repository = "https://github.com/jiwei/pdf-highlighter.git"
"Bug Tracker" = "https://github.com/jiwei/pdf-highlighter/issues"
Changelog = "https://github.com/jiwei/pdf-highlighter/blob/main/CHANGELOG.md"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0,<9.0.0",
    "pytest-cov>=4.0.0,<7.0.0",
    "black>=23.0.0,<25.0.0",
    "isort>=5.12.0,<6.0.0",
    "flake8>=6.0.0,<8.0.0",
    "mypy>=1.0.0,<2.0.0",
]
test = [
    "pytest>=7.0.0,<9.0.0",
    "pytest-cov>=4.0.0,<7.0.0",
    "pytest-mock>=3.10.0,<4.0.0",
]
docs = [
    "sphinx>=6.0.0,<8.0.0",
    "sphinx-rtd-theme>=1.2.0,<4.0.0",
    "myst-parser>=1.0.0,<5.0.0",
]
ocr = [
    "pytesseract>=0.3.10,<1.0.0",
    "Pillow>=9.0.0,<12.0.0",
]

[project.scripts]
pdf-highlight = "pdf_highlighter.cli.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.version]
path = "src/pdf_highlighter/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["src/pdf_highlighter"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
    "/LICENSE",
    "/CHANGELOG.md",
]

# 代码格式化配置
[tool.black]
line-length = 100
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# 导入排序配置
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# 类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# 测试配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# 覆盖率配置
[tool.coverage.run]
source = ["src"]
branch = true
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
