#!/usr/bin/env python3
"""
简单的ID功能测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from pdf_highlighter import MultiKeywordHighlighter
    
    # 找到PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到PDF文件")
        exit(1)
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 创建高亮器
    highlighter = MultiKeywordHighlighter(str(test_file))
    
    # 测试添加关键词
    id1 = highlighter.add_keyword("测试", color="red", id="custom_test")
    print(f"添加关键词成功，返回ID: {id1}")
    
    # 检查配置
    config = highlighter.keyword_configs[0]
    print(f"关键词配置:")
    print(f"  ID: {config.id}")
    print(f"  文本: {config.text}")
    print(f"  颜色: {config.color}")
    
    # 测试自动生成ID
    id2 = highlighter.add_keyword("自动ID", color="blue")
    print(f"自动生成ID: {id2}")
    
    print("✅ ID功能基本测试通过")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
