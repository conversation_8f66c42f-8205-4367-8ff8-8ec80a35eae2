{"description": "增强功能配置文件示例 - 展示页面信息和模糊匹配功能", "version": "2.1", "keywords": [{"id": "exact_match_keyword", "text": "精确匹配", "color": "yellow", "opacity": 0.6, "pages": "1-3", "comment": "普通精确匹配关键词"}, {"id": "fuzzy_match_keyword_1", "text": "ABCDEFGH", "color": "red", "opacity": 0.7, "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "comment": "模糊匹配：AB.*GH"}, {"id": "fuzzy_match_keyword_2", "text": "测试关键词", "color": "blue", "opacity": 0.8, "fuzzy_enabled": true, "fuzzy_start_chars": 1, "fuzzy_end_chars": 1, "comment": "模糊匹配：测.*词"}, {"id": "context_with_fuzzy", "text": "审核", "color": "green", "opacity": 0.6, "context_phrase": "添加以供审核", "fuzzy_enabled": true, "fuzzy_start_chars": 1, "fuzzy_end_chars": 1, "comment": "上下文匹配 + 模糊匹配：审.*核"}, {"id": "global_fuzzy_settings", "text": "全局设置", "color": "orange", "opacity": 0.5, "comment": "使用全局模糊匹配设置"}, {"id": "regex_keyword", "text": "前端|后端|全栈", "color": "purple", "opacity": 0.7, "regex": true, "comment": "正则表达式关键词，不会应用模糊匹配"}, {"id": "disabled_fuzzy", "text": "禁用模糊", "color": "cyan", "opacity": 0.4, "fuzzy_enabled": false, "comment": "明确禁用模糊匹配的关键词"}], "global_options": {"fuzzy_matching_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "context_similarity_threshold": 0.8, "context_fallback_strategy": "fallback_to_normal", "output_suffix": "_enhanced_features"}, "feature_explanations": {"page_information": {"description": "每个关键词的处理结果现在包含页面信息", "successful_keywords": "记录实际被标记的页面号码列表", "failed_keywords": "记录尝试搜索的页面范围", "example_output": {"keywords_by_id": {"exact_match_keyword": {"id": "exact_match_keyword", "text": "精确匹配", "highlights_added": 3, "pages": [1, 2, 3], "status": "success"}, "failed_keyword": {"id": "failed_keyword", "text": "不存在的关键词", "highlights_added": 0, "pages": [1, 2, 3], "status": "no_matches"}}}}, "fuzzy_matching": {"description": "模糊匹配功能允许关键词部分匹配", "how_it_works": "提取关键词开头和结尾的指定字符数，生成正则表达式", "examples": [{"keyword": "ABCDEFGH", "start_chars": 2, "end_chars": 2, "generated_regex": "AB[\\s\\S]*?GH", "matches": ["ABCDEFGH", "AB123GH", "AB任意内容GH", "AB\n换行内容\nGH"]}, {"keyword": "测试关键词", "start_chars": 1, "end_chars": 1, "generated_regex": "测[\\s\\S]*?词", "matches": ["测试关键词", "测试词", "测量关键词", "测\n试\n关键词"]}, {"keyword": "本书可以作为高校教育技术学专业教材", "start_chars": 2, "end_chars": 2, "generated_regex": "本书[\\s\\S]*?教材", "matches": ["本书可以作为高校教育技术学专业教材", "本书\n可以作为高校\n教育技术学专业\n教材"]}], "configuration_levels": {"global": "在global_options中设置默认值", "keyword": "在关键词级别覆盖全局设置", "priority": "关键词级别配置优先于全局配置"}, "compatibility": {"with_regex": "已设置regex=true的关键词不会应用模糊匹配", "with_context": "可以与上下文匹配功能结合使用", "fallback": "关键词长度不足时自动回退到精确匹配"}}}, "usage_examples": {"python_api": ["# 基本用法", "highlighter = MultiKeywordHighlighter('document.pdf')", "highlighter.load_config_file('enhanced_features_config.json')", "result = highlighter.process()", "", "# 检查页面信息", "for keyword_id, stats in result['keywords_by_id'].items():", "    print(f'关键词: {stats[\"text\"]}')", "    print(f'标记页面: {stats[\"pages\"]}')", "    print(f'状态: {stats[\"status\"]}')", "", "# 编程方式添加模糊匹配关键词", "highlighter.add_keyword(", "    text='编程关键词',", "    color='pink',", "    fuzzy_enabled=True,", "    fuzzy_start_chars=2,", "    fuzzy_end_chars=2", ")"]}, "expected_results": {"api_response_structure": {"keywords_by_id": {"keyword_id": {"id": "关键词ID", "text": "关键词文本", "matches_found": "找到的匹配数量", "highlights_added": "成功添加的高亮数量", "pages": "页面号码列表（新增字段）", "status": "处理状态", "color": "高亮颜色", "opacity": "透明度"}}}, "fuzzy_matching_logs": ["INFO - 设置全局模糊匹配: 启用", "INFO - 设置全局模糊匹配开头字符数: 2", "INFO - 设置全局模糊匹配结尾字符数: 2", "DEBUG - 为关键词 'ABCDEFGH' 生成模糊匹配正则表达式: 'AB[\\s\\S]*?GH' (支持换行符)", "INFO - 对关键词 'ABCDEFGH' 应用模糊匹配，生成正则表达式: 'AB[\\s\\S]*?GH'"]}, "troubleshooting": {"common_issues": [{"issue": "模糊匹配没有生效", "possible_causes": ["全局模糊匹配未启用", "关键词级别设置fuzzy_enabled=false", "关键词已设置regex=true", "关键词长度不足"], "solutions": ["检查global_options中的fuzzy_matching_enabled设置", "检查关键词的fuzzy_enabled配置", "移除regex=true设置或设置fuzzy_enabled=true", "增加关键词长度或减少start_chars/end_chars"]}, {"issue": "页面信息不准确", "possible_causes": ["页面范围配置错误", "上下文匹配失败"], "solutions": ["检查pages配置格式", "验证上下文短语是否存在于PDF中"]}]}}