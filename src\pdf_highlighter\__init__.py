"""
PDF关键词高亮工具

基于PyMuPDF的PDF文件处理工具，支持多格式PDF文件的关键词高亮功能。

主要特性：
- 支持多种来源生成的PDF文件（Word、Excel、图片转换等）
- 关键词搜索和高亮注释功能
- 支持正则表达式、大小写敏感、完整单词匹配
- 页码范围和关键词序号过滤
- 自定义高亮颜色和透明度
- 命令行接口，易于使用和集成
- 完善的错误处理和日志记录

使用示例：
    >>> from pdf_highlighter import PDFHighlighter
    >>> with PDFHighlighter('document.pdf') as highlighter:
    ...     results = highlighter.search_keywords('关键词')
    ...     highlighter.add_highlights(results)
    ...     highlighter.save_pdf('highlighted.pdf')

命令行使用：
    $ pdf-highlight input.pdf "关键词" -o output.pdf
    $ pdf-highlight input.pdf "前端|后端" --regex -c red
"""

# 版本信息
__version__ = "0.1.0"
__author__ = "jiwei"
__email__ = "<EMAIL>"
__license__ = "MIT"
__copyright__ = "Copyright (c) 2025 jiwei"

# 包信息
__title__ = "pdf-highlighter"
__description__ = "A PDF keyword highlighting tool based on PyMuPDF"
__url__ = "https://github.com/jiwei/pdf-highlighter"

# 导出主要类和函数
from pdf_highlighter.cli.main import main
from pdf_highlighter.core.highlighter import PDFHighlighter
from pdf_highlighter.core.multi_highlighter import MultiKeywordHighlighter, KeywordConfig
from pdf_highlighter.core.searcher import TextSearcher
from pdf_highlighter.core.validator import ParameterValidator
from pdf_highlighter.config.settings import get_settings, get_config, set_config
from pdf_highlighter.utils.logger import get_logger, setup_logging
from pdf_highlighter.utils.exceptions import (
    PDFHighlighterError,
    PDFError,
    SearchError,
    ValidationError,
    HighlightError,
    ConfigurationError,
)

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    "__title__",
    "__description__",
    "__url__",

    # 主要功能
    "main",
    "PDFHighlighter",
    "MultiKeywordHighlighter",
    "KeywordConfig",
    "TextSearcher",
    "ParameterValidator",

    # 配置管理
    "get_settings",
    "get_config",
    "set_config",

    # 日志系统
    "get_logger",
    "setup_logging",

    # 异常类
    "PDFHighlighterError",
    "PDFError",
    "SearchError",
    "ValidationError",
    "HighlightError",
    "ConfigurationError",
]


def get_version() -> str:
    """获取版本信息

    Returns:
        str: 版本号
    """
    return __version__


def get_package_info() -> dict:
    """获取包信息

    Returns:
        dict: 包的详细信息
    """
    return {
        "name": __title__,
        "version": __version__,
        "description": __description__,
        "author": __author__,
        "email": __email__,
        "license": __license__,
        "url": __url__,
        "copyright": __copyright__,
    }
