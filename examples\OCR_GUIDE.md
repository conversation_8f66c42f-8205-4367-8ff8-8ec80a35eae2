# PDF OCR高亮功能使用指南

本指南介绍如何使用pdf-highlighter的OCR功能来识别和高亮PDF文档中图片内的文字。

## 功能概述

OCR（Optical Character Recognition，光学字符识别）功能允许pdf-highlighter：
- 识别PDF文档中嵌入图片内的文字
- 对识别出的文字进行关键词搜索
- 在匹配的文字位置添加高亮注释
- 支持中文、英文等多种语言识别
- 支持正则表达式搜索图片中的文字

## 安装依赖

### 1. 安装Python依赖

```bash
# 安装OCR相关依赖
pip install pdf-highlighter[ocr]

# 或者手动安装
pip install pytesseract Pillow
```

### 2. 安装Tesseract OCR引擎

#### Windows
1. 下载Tesseract安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 运行安装程序，记住安装路径
3. 将Tesseract安装目录添加到系统PATH环境变量

#### macOS
```bash
brew install tesseract
```

#### Ubuntu/Debian
```bash
sudo apt-get install tesseract-ocr
```

### 3. 安装中文语言包

#### Windows
在Tesseract安装时选择中文语言包，或者：
1. 下载中文语言包文件：https://github.com/tesseract-ocr/tessdata
2. 将`chi_sim.traineddata`复制到Tesseract的tessdata目录

#### macOS/Linux
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr-chi-sim

# macOS
brew install tesseract-lang
```

## 基本使用

### 命令行使用

```bash
# 启用OCR搜索图片中的文字
pdf-highlight input.pdf "关键词" --enable-ocr

# 仅搜索图片中的文字，不搜索PDF文本
pdf-highlight input.pdf "图片文字" --ocr-only

# 指定OCR语言
pdf-highlight input.pdf "中文" --enable-ocr --ocr-lang chi_sim

# 设置OCR置信度阈值
pdf-highlight input.pdf "文字" --enable-ocr --ocr-confidence 70

# 结合其他选项使用
pdf-highlight input.pdf "关键词" --enable-ocr -p 1-5 -c red -a 0.7
```

### Python API使用

```python
from pdf_highlighter import PDFHighlighter

# 基本OCR高亮
with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    # 搜索关键词（包括图片中的文字）
    results = highlighter.search_keywords('关键词', enable_ocr=True)
    
    # 添加高亮
    highlighter.add_highlights(results, color=(1, 1, 0), opacity=0.6)
    
    # 保存文件
    highlighter.save_pdf('highlighted.pdf')

# 仅搜索图片中的文字
with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    results = highlighter.search_in_images_only('图片文字')
    highlighter.add_highlights(results, color=(0, 1, 0))
    highlighter.save_pdf('ocr_only.pdf')
```

## 高级配置

### OCR参数配置

```python
from pdf_highlighter.config.settings import set_config

# 设置OCR语言
set_config('ocr.language', 'chi_sim+eng')  # 中文+英文

# 设置置信度阈值
set_config('ocr.confidence_threshold', 70)  # 0-100

# 图片预处理设置
set_config('ocr.image_preprocessing.scale_factor', 2.0)  # 图片放大倍数
set_config('ocr.image_preprocessing.enable_denoising', True)  # 启用去噪
set_config('ocr.image_preprocessing.enable_binarization', True)  # 启用二值化

# 性能设置
set_config('ocr.parallel_processing', True)  # 启用并行处理
set_config('ocr.max_workers', 4)  # 最大工作线程数
set_config('ocr.cache_enabled', True)  # 启用结果缓存
```

### 支持的语言代码

| 语言 | 代码 | 说明 |
|------|------|------|
| 英文 | eng | 默认支持 |
| 中文简体 | chi_sim | 需要安装语言包 |
| 中文繁体 | chi_tra | 需要安装语言包 |
| 日文 | jpn | 需要安装语言包 |
| 韩文 | kor | 需要安装语言包 |
| 多语言 | chi_sim+eng | 组合使用 |

## 使用示例

### 1. 基本文字识别

```python
from pdf_highlighter import PDFHighlighter

with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    # 搜索中文关键词
    results = highlighter.search_keywords('产品说明', enable_ocr=True)
    
    if results:
        print(f"找到 {sum(len(quads) for _, quads in results)} 个匹配项")
        highlighter.add_highlights(results)
        highlighter.save_pdf('highlighted.pdf')
```

### 2. 多语言混合搜索

```python
from pdf_highlighter.config.settings import set_config

# 配置多语言支持
set_config('ocr.language', 'chi_sim+eng')

with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    # 搜索中文
    chinese_results = highlighter.search_keywords('中文', enable_ocr=True)
    
    # 搜索英文
    english_results = highlighter.search_keywords('English', enable_ocr=True)
    
    # 使用不同颜色高亮
    highlighter.add_highlights(chinese_results, color=(1, 0, 0))  # 红色
    highlighter.add_highlights(english_results, color=(0, 0, 1))  # 蓝色
    
    highlighter.save_pdf('multilingual.pdf')
```

### 3. 正则表达式搜索

```python
with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    # 搜索电话号码格式
    phone_results = highlighter.search_keywords(
        r'\d{3}-\d{4}-\d{4}',  # 格式：123-4567-8901
        enable_ocr=True,
        regex=True
    )
    
    # 搜索邮箱地址
    email_results = highlighter.search_keywords(
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        enable_ocr=True,
        regex=True
    )
    
    highlighter.add_highlights(phone_results, color=(1, 1, 0))  # 黄色
    highlighter.add_highlights(email_results, color=(0, 1, 1))  # 青色
    highlighter.save_pdf('regex_highlighted.pdf')
```

### 4. 指定页面范围

```python
with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    # 只在前5页搜索
    results = highlighter.search_keywords(
        '关键词',
        pages=[1, 2, 3, 4, 5],
        enable_ocr=True
    )
    
    highlighter.add_highlights(results)
    highlighter.save_pdf('page_range.pdf')
```

## 性能优化建议

### 1. 图片预处理
- 适当调整`scale_factor`提高OCR精度（建议2.0-3.0）
- 启用去噪和二值化处理
- 对于低质量图片，可以尝试更高的放大倍数

### 2. 并行处理
- 对于包含多张图片的PDF，启用并行处理
- 根据CPU核心数调整`max_workers`参数
- 大文件处理时建议启用缓存

### 3. 置信度设置
- 默认置信度60适合大多数情况
- 对于高质量图片，可以提高到70-80
- 对于低质量图片，可以降低到40-50

## 故障排除

### 常见问题

1. **ImportError: No module named 'pytesseract'**
   ```bash
   pip install pytesseract Pillow
   ```

2. **TesseractNotFoundError**
   - 确保Tesseract已正确安装
   - 检查PATH环境变量
   - Windows用户可能需要设置tesseract路径：
   ```python
   import pytesseract
   pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
   ```

3. **语言包错误**
   - 确保已安装所需的语言包
   - 检查tessdata目录是否包含对应的.traineddata文件

4. **OCR识别精度低**
   - 调整图片预处理参数
   - 提高图片放大倍数
   - 降低置信度阈值
   - 检查图片质量和分辨率

### 调试技巧

```python
# 启用详细日志
from pdf_highlighter.utils.logger import setup_logging
setup_logging(level='DEBUG')

# 检查OCR缓存状态
with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    ocr_processor = highlighter._get_ocr_processor()
    stats = ocr_processor.get_cache_stats()
    print(f"缓存状态: {stats}")
```

## 注意事项

1. **性能影响**：OCR处理比普通文本搜索慢，建议仅在需要时启用
2. **内存使用**：处理大量图片时会占用较多内存
3. **准确性**：OCR识别准确性取决于图片质量，可能存在误识别
4. **语言支持**：确保安装了所需语言的训练数据
5. **版权考虑**：使用OCR功能时请遵守相关版权法律法规

## 更多示例

完整的示例代码请参考：
- `examples/ocr_example.py` - 基本OCR功能演示
- `examples/advanced_ocr.py` - 高级OCR配置和使用
- `examples/batch_ocr.py` - 批量OCR处理示例
