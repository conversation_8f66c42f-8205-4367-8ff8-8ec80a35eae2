#!/usr/bin/env python3
"""
上下文匹配功能测试脚本

演示如何使用上下文短语来精确匹配关键词，实现：
1. 只在特定上下文中高亮关键词
2. 避免误匹配其他位置的相同关键词
3. 支持页面级别和上下文级别的双重过滤
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
logger = get_logger()


def create_test_context_config():
    """创建测试用的上下文匹配配置文件"""
    config = {
        "keywords": [
            {
                "text": "的",
                "color": "yellow",
                "opacity": 0.6,
                "pages": "1",
                "context_phrase": "这是一个测试的文档"
            },
            {
                "text": "是",
                "color": "blue",
                "opacity": 0.7,
                "context_phrase": "这是重要的内容"
            },
            {
                "text": "在",
                "color": "green",
                "opacity": 0.5,
                "pages": "2-3",
                "context_phrase": "在这个位置"
            },
            {
                "text": "了",
                "color": "red",
                "opacity": 0.8,
                "context_phrase": "完成了任务"
            },
            {
                "text": "普通关键词",
                "color": "gray",
                "opacity": 0.4
            }
        ],
        "global_options": {
            "pages": None,
            "occurrence": None,
            "whole_word": False,
            "output_suffix": "_context_test"
        }
    }
    
    config_file = project_root / "examples" / "test_context_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"测试配置文件已创建: {config_file}")
    return config_file


def test_context_matching():
    """测试上下文匹配功能"""
    print("=== 上下文匹配功能测试 ===\n")
    
    # 创建测试配置文件
    config_file = create_test_context_config()
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件，请在项目根目录放置一个PDF文件进行测试")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 创建多关键词高亮处理器
        highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 加载上下文匹配配置
        highlighter.load_config_file(str(config_file))
        
        # 显示配置摘要
        print("\n配置摘要:")
        for i, config in enumerate(highlighter.keyword_configs, 1):
            pages_info = f" (页面: {config.pages})" if config.pages else " (所有页面)"
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else ""
            print(f"  {i}. '{config.text}' -> {config.color}{pages_info}{context_info}")
        
        # 执行处理
        output_file = project_root / f"context_test_highlighted_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file}")
        
        result = highlighter.process(str(output_file))
        
        if result['success']:
            print(f"✅ 处理成功！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print("\n各关键词处理结果:")
            for keyword, info in result['keywords_processed'].items():
                print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
        else:
            print(f"❌ 处理失败: {result['error']}")
        
        # 演示编程API方式
        print("\n=== 编程API方式演示 ===")
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        
        # 添加上下文匹配关键词配置
        highlighter2.add_keyword("的", color="yellow", context_phrase="这是一个测试的文档")
        highlighter2.add_keyword("是", color="blue", context_phrase="这是重要的内容")
        highlighter2.add_keyword("普通", color="green")  # 没有上下文限制
        
        print("编程API配置:")
        for i, config in enumerate(highlighter2.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文限制)"
            print(f"  {i}. '{config.text}' -> {config.color}{context_info}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 上下文匹配功能测试完成！")
    return True


if __name__ == "__main__":
    # 运行测试
    test_context_matching()
    
    print("\n" + "="*60)
    print("上下文匹配功能特点:")
    print("1. ✅ 支持在特定上下文短语中精确匹配关键词")
    print("2. ✅ 避免误匹配其他位置的相同关键词")
    print("3. ✅ 支持页面级别和上下文级别的双重过滤")
    print("4. ✅ 兼容现有的所有功能（页面范围、颜色、透明度等）")
    print("5. ✅ 向后兼容，没有上下文短语的关键词按原逻辑处理")
    print("="*60)
