"""
参数验证模块

提供全面的参数验证功能，确保输入参数的有效性和安全性。
"""

import os
import re
from typing import List, Tuple, Optional, Union
import pymupdf


class ParameterValidator:
    """参数验证器
    
    提供各种参数的验证功能，包括文件路径、页码范围、颜色值等。
    """
    
    @staticmethod
    def validate_pdf_path(pdf_path: str) -> str:
        """验证PDF文件路径
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            str: 规范化的文件路径
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件不是有效的PDF格式
            PermissionError: 没有读取权限
        """
        if not pdf_path or not pdf_path.strip():
            raise ValueError("PDF文件路径不能为空")
        
        pdf_path = pdf_path.strip()
        
        # 检查文件是否存在
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
        
        # 检查是否是文件而不是目录
        if not os.path.isfile(pdf_path):
            raise ValueError(f"指定路径不是文件: {pdf_path}")
        
        # 检查读取权限
        if not os.access(pdf_path, os.R_OK):
            raise PermissionError(f"没有读取PDF文件的权限: {pdf_path}")
        
        # 尝试打开PDF文件验证格式
        try:
            doc = pymupdf.open(pdf_path)
            doc.close()
        except Exception as e:
            raise ValueError(f"无法打开PDF文件，可能文件已损坏或格式不正确: {e}")
        
        return os.path.abspath(pdf_path)
    
    @staticmethod
    def validate_output_path(output_path: str, input_path: str) -> str:
        """验证输出文件路径
        
        Args:
            output_path: 输出文件路径
            input_path: 输入文件路径（用于生成默认输出路径）
            
        Returns:
            str: 规范化的输出文件路径
            
        Raises:
            ValueError: 路径无效
            PermissionError: 没有写入权限
        """
        if not output_path or not output_path.strip():
            # 生成默认输出路径
            base_name = os.path.splitext(input_path)[0]
            output_path = f"{base_name}_highlighted.pdf"
        
        output_path = output_path.strip()
        
        # 确保输出文件有.pdf扩展名
        if not output_path.lower().endswith('.pdf'):
            output_path += '.pdf'
        
        # 检查输出目录是否存在，不存在则创建
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                raise PermissionError(f"无法创建输出目录: {e}")
        
        # 检查写入权限
        if output_dir:
            if not os.access(output_dir, os.W_OK):
                raise PermissionError(f"没有写入目录的权限: {output_dir}")
        
        return os.path.abspath(output_path)
    
    @staticmethod
    def validate_pages(pages_str: str, total_pages: int) -> List[int]:
        """验证页码范围字符串
        
        Args:
            pages_str: 页码范围字符串，如 "1,3-5,7"
            total_pages: PDF总页数
            
        Returns:
            List[int]: 有效的页码列表
            
        Raises:
            ValueError: 页码格式无效或超出范围
        """
        if not pages_str or not pages_str.strip():
            return list(range(1, total_pages + 1))
        
        pages_str = pages_str.strip()
        pages = []
        
        try:
            # 分割逗号分隔的部分
            parts = [part.strip() for part in pages_str.split(',')]
            
            for part in parts:
                if '-' in part:
                    # 处理范围，如 "3-5"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())
                    
                    if start > end:
                        raise ValueError(f"页码范围无效: {part}，起始页码不能大于结束页码")
                    
                    pages.extend(range(start, end + 1))
                else:
                    # 处理单个页码
                    page = int(part)
                    pages.append(page)
            
            # 去重并排序
            pages = sorted(set(pages))
            
            # 验证页码范围
            for page in pages:
                if page < 1 or page > total_pages:
                    raise ValueError(f"页码超出范围: {page}，有效范围是 1-{total_pages}")
            
            return pages
            
        except ValueError as e:
            if "invalid literal for int()" in str(e):
                raise ValueError(f"页码格式无效: {pages_str}，请使用格式如 '1,3-5,7'")
            raise
    
    @staticmethod
    def validate_occurrence(occurrence: Optional[Union[int, str]], total_matches: int) -> Optional[int]:
        """验证关键词序号参数
        
        Args:
            occurrence: 关键词序号
            total_matches: 总匹配数
            
        Returns:
            Optional[int]: 有效的序号，None表示所有匹配项
            
        Raises:
            ValueError: 序号无效或超出范围
        """
        if occurrence is None:
            return None
        
        try:
            if isinstance(occurrence, str):
                occurrence = int(occurrence.strip())
            
            if occurrence < 1:
                raise ValueError("关键词序号必须大于0")
            
            if occurrence > total_matches:
                raise ValueError(f"关键词序号超出范围: {occurrence}，总共只有 {total_matches} 个匹配项")
            
            return occurrence
            
        except ValueError as e:
            if "invalid literal for int()" in str(e):
                raise ValueError(f"关键词序号格式无效: {occurrence}，必须是正整数")
            raise
    
    @staticmethod
    def validate_color(color: Union[str, Tuple[float, float, float]]) -> Tuple[float, float, float]:
        """验证颜色格式
        
        Args:
            color: 颜色值，可以是颜色名称或RGB元组
            
        Returns:
            Tuple[float, float, float]: RGB颜色值(0-1)
            
        Raises:
            ValueError: 颜色格式无效
        """
        # 预定义颜色
        color_map = {
            'yellow': (1, 1, 0),
            'red': (1, 0, 0),
            'green': (0, 1, 0),
            'blue': (0, 0, 1),
            'orange': (1, 0.5, 0),
            'purple': (0.5, 0, 1),
            'pink': (1, 0, 1),
            'cyan': (0, 1, 1),
            'gray': (0.5, 0.5, 0.5),
            'black': (0, 0, 0),
            'white': (1, 1, 1)
        }
        
        if isinstance(color, str):
            color = color.lower().strip()
            
            # 检查预定义颜色
            if color in color_map:
                return color_map[color]
            
            # 检查十六进制颜色格式 #RRGGBB
            if color.startswith('#'):
                if len(color) != 7:
                    raise ValueError(f"十六进制颜色格式无效: {color}，应为 #RRGGBB 格式")
                
                try:
                    r = int(color[1:3], 16) / 255.0
                    g = int(color[3:5], 16) / 255.0
                    b = int(color[5:7], 16) / 255.0
                    return (r, g, b)
                except ValueError:
                    raise ValueError(f"十六进制颜色格式无效: {color}")
            
            # 检查RGB格式 "r,g,b"
            if ',' in color:
                try:
                    parts = [float(part.strip()) for part in color.split(',')]
                    if len(parts) != 3:
                        raise ValueError("RGB格式应包含3个值")
                    
                    # 如果值大于1，假设是0-255范围，转换为0-1
                    if any(v > 1 for v in parts):
                        parts = [v / 255.0 for v in parts]
                    
                    return tuple(parts)
                except ValueError:
                    raise ValueError(f"RGB颜色格式无效: {color}")
            
            raise ValueError(f"不支持的颜色格式: {color}")
        
        elif isinstance(color, (tuple, list)):
            if len(color) != 3:
                raise ValueError("RGB颜色元组必须包含3个值")
            
            try:
                r, g, b = [float(v) for v in color]
                
                # 如果值大于1，假设是0-255范围
                if any(v > 1 for v in [r, g, b]):
                    r, g, b = r / 255.0, g / 255.0, b / 255.0
                
                # 验证范围
                if not all(0 <= v <= 1 for v in [r, g, b]):
                    raise ValueError("RGB值必须在0-1或0-255范围内")
                
                return (r, g, b)
            except (ValueError, TypeError):
                raise ValueError(f"RGB颜色格式无效: {color}")
        
        else:
            raise ValueError(f"不支持的颜色类型: {type(color)}")
    
    @staticmethod
    def validate_opacity(opacity: Union[float, str]) -> float:
        """验证透明度值
        
        Args:
            opacity: 透明度值(0-1)
            
        Returns:
            float: 有效的透明度值
            
        Raises:
            ValueError: 透明度值无效
        """
        try:
            if isinstance(opacity, str):
                opacity = float(opacity.strip())
            
            opacity = float(opacity)
            
            if not 0 <= opacity <= 1:
                raise ValueError(f"透明度值必须在0-1范围内: {opacity}")
            
            return opacity
            
        except ValueError as e:
            if "could not convert" in str(e):
                raise ValueError(f"透明度格式无效: {opacity}，必须是0-1之间的数字")
            raise
    
    @staticmethod
    def validate_keyword(keyword: str) -> str:
        """验证搜索关键词
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            str: 有效的关键词
            
        Raises:
            ValueError: 关键词无效
        """
        if not keyword or not keyword.strip():
            raise ValueError("搜索关键词不能为空")
        
        keyword = keyword.strip()
        
        # 检查关键词长度
        if len(keyword) > 1000:
            raise ValueError("搜索关键词过长，最大长度为1000字符")
        
        return keyword
    
    @staticmethod
    def validate_annotation_style(style: str) -> str:
        """验证标记样式
         
        Args:
            style: 标记样式
            
        Returns:
            str: 有效的标记样式
            
        Raises:
            ValueError: 标记样式无效
        """
        if not style or not style.strip():
            raise ValueError("标记样式不能为空")
        
        style = style.strip().lower()
        
        # 支持的标记样式
        supported_styles = ["highlight", "underline", "strikeout", "squiggly"]
        
        if style not in supported_styles:
            raise ValueError(f"不支持的标记样式: {style}，支持的样式包括: {', '.join(supported_styles)}")
        
        return style
