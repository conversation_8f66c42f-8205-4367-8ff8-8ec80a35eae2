{"keywords": [{"text": "前端", "color": "yellow", "opacity": 0.6, "case_sensitive": false, "regex": false}, {"text": "后端", "color": "blue", "opacity": 0.7, "case_sensitive": false, "regex": false}, {"text": "工程师", "color": "green", "opacity": 0.5, "case_sensitive": false, "regex": false}, {"text": "开发|编程", "color": "orange", "opacity": 0.6, "case_sensitive": false, "regex": true}, {"text": "Python", "color": "#FF6B6B", "opacity": 0.8, "case_sensitive": true, "regex": false}], "global_options": {"pages": null, "occurrence": null, "whole_word": false, "overwrite": false}, "processing": {"max_workers": 4, "recursive": true, "output_suffix": "_highlighted", "skip_existing": true}, "logging": {"level": "INFO", "file": "multi_highlight.log", "console": true}}