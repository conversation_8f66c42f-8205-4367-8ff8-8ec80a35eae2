#!/usr/bin/env python3
"""
上下文短语相似度匹配功能测试脚本

测试新增的相似度匹配功能：
1. 全局相似度阈值设置
2. 局部相似度阈值设置
3. 模糊匹配效果验证
4. 向后兼容性验证
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置详细日志
logging.basicConfig(level=logging.INFO)
logger = get_logger()


def create_similarity_test_configs():
    """创建相似度测试配置文件"""
    
    # 配置1：全局相似度阈值
    config1 = {
        "keywords": [
            {
                "id": "review_fuzzy",
                "text": "审核", 
                "color": "blue",
                "context_phrase": "添加 以供 审核",  # 多空格版本
                "opacity": 0.7
            },
            {
                "id": "submit_fuzzy",
                "text": "提交",
                "color": "green",
                "context_phrase": "确认无误后提交",  # 可能在PDF中是"请确认无误后提交"
                "opacity": 0.6
            }
        ],
        "global_options": {
            "context_similarity_threshold": 0.8,
            "output_suffix": "_similarity_global"
        }
    }
    
    # 配置2：局部相似度阈值
    config2 = {
        "keywords": [
            {
                "id": "review_low_threshold",
                "text": "审核",
                "color": "blue", 
                "context_phrase": "添加 以供 审核",
                "similarity_threshold": 0.7,   # 低阈值
                "opacity": 0.7
            },
            {
                "id": "app_context",
                "text": "应用",
                "color": "green",
                "context_phrase": "苹果ipa应用",
                "similarity_threshold": 0.9,
                "opacity": 0.6
            }
        ],
        "global_options": {
            "context_similarity_threshold": 1.0,  # 全局精确匹配
            "output_suffix": "_similarity_local"
        }
    }
    
    # 配置3：向后兼容性测试
    config3 = {
        "keywords": [
            {
                "id": "backward_compatible",
                "text": "审核",
                "color": "red",
                "context_phrase": "添加以供审核",
                "opacity": 0.8
            },
            {
                "id": "no_context",
                "text": "应用",
                "color": "green",
                "opacity": 0.6
            }
        ],
        "global_options": {
            "output_suffix": "_backward_compatible"
        }
    }
    
    configs = [
        ("similarity_global_config.json", config1),
        ("similarity_local_config.json", config2),
        ("backward_compatible_config.json", config3)
    ]
    
    config_files = []
    for filename, config in configs:
        config_file = project_root / "examples" / filename
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        config_files.append(config_file)
        print(f"创建配置文件: {config_file}")
    
    return config_files


def test_similarity_matching():
    """测试相似度匹配功能"""
    print("=== 上下文短语相似度匹配功能测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 创建测试配置文件
    config_files = create_similarity_test_configs()
    
    test_scenarios = [
        ("全局相似度阈值测试", config_files[0]),
        ("局部相似度阈值测试", config_files[1]),
        ("向后兼容性测试", config_files[2])
    ]
    
    results = []
    
    for scenario_name, config_file in test_scenarios:
        print(f"\n=== {scenario_name} ===")
        
        try:
            highlighter = MultiKeywordHighlighter(str(test_file))
            highlighter.load_config_file(str(config_file))
            
            print(f"全局相似度阈值: {highlighter.global_similarity_threshold}")
            
            print("配置的关键词:")
            for i, config in enumerate(highlighter.keyword_configs, 1):
                threshold = config.similarity_threshold if config.similarity_threshold is not None else highlighter.global_similarity_threshold
                context_info = f" (上下文: {config.context_phrase}, 阈值: {threshold})" if config.context_phrase else " (无上下文)"
                print(f"  {i}. [{config.id}] '{config.text}' -> {config.color}{context_info}")
            
            # 执行处理
            output_file = project_root / f"similarity_test_{scenario_name.replace(' ', '_')}_{test_file.name}"
            print(f"\n开始处理，输出文件: {output_file}")
            
            result = highlighter.process(str(output_file))
            
            if result['success']:
                print(f"✅ 处理成功！总共添加 {result['total_highlights']} 个高亮")
                
                # 显示归类结果
                keywords_by_status = result.get('keywords_by_status', {})
                successful = keywords_by_status.get('successful', {})
                failed = keywords_by_status.get('failed', {})
                
                print(f"\n📊 处理结果:")
                print(f"✅ 成功高亮的关键词 ({len(successful)} 个):")
                for keyword_id, stats in successful.items():
                    print(f"  [{keyword_id}] '{stats['text']}' -> {stats['highlights_added']} 个高亮")
                
                print(f"❌ 未被高亮的关键词 ({len(failed)} 个):")
                for keyword_id, stats in failed.items():
                    print(f"  [{keyword_id}] '{stats['text']}' -> {stats['status']}")
                
                results.append({
                    'scenario': scenario_name,
                    'total_highlights': result['total_highlights'],
                    'successful_count': len(successful),
                    'failed_count': len(failed)
                })
            else:
                print(f"❌ 处理失败: {result['error']}")
                results.append({
                    'scenario': scenario_name,
                    'total_highlights': 0,
                    'successful_count': 0,
                    'failed_count': 0
                })
        
        except Exception as e:
            logger.error(f"{scenario_name} 失败: {e}")
            import traceback
            traceback.print_exc()
            results.append({
                'scenario': scenario_name,
                'total_highlights': 0,
                'successful_count': 0,
                'failed_count': 0
            })
    
    # 结果对比
    print(f"\n=== 测试结果对比 ===")
    print("| 测试场景 | 总高亮数 | 成功关键词 | 失败关键词 |")
    print("|----------|----------|------------|------------|")
    
    for result in results:
        scenario = result['scenario']
        highlights = result['total_highlights']
        successful = result['successful_count']
        failed = result['failed_count']
        
        print(f"| {scenario} | {highlights} | {successful} | {failed} |")
    
    print("\n✅ 相似度匹配功能测试完成！")
    return True


def test_api_similarity_matching():
    """测试编程API的相似度匹配功能"""
    print(f"\n=== 编程API相似度匹配测试 ===")
    
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        return
    
    test_file = test_files[0]
    
    try:
        # 测试1：全局相似度阈值
        print("测试1 - 全局相似度阈值:")
        highlighter1 = MultiKeywordHighlighter(str(test_file))
        highlighter1.global_similarity_threshold = 0.8
        
        id1 = highlighter1.add_keyword("审核", color="red", context_phrase="添加以供审核")
        id2 = highlighter1.add_keyword("应用", color="green", context_phrase="苹果ipa应用")
        
        print(f"  全局相似度阈值: {highlighter1.global_similarity_threshold}")
        print(f"  添加关键词: {id1}, {id2}")
        
        result1 = highlighter1.process()
        if result1['success']:
            print(f"  ✅ 成功，添加 {result1['total_highlights']} 个高亮")
        
        # 测试2：局部相似度阈值
        print("\n测试2 - 局部相似度阈值:")
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        
        id3 = highlighter2.add_keyword("审核", color="red", context_phrase="添加以供审核", similarity_threshold=0.9)
        id4 = highlighter2.add_keyword("提交", color="blue", context_phrase="确认无误后提交", similarity_threshold=0.7)
        
        print(f"  关键词 {id3} 相似度阈值: 0.9")
        print(f"  关键词 {id4} 相似度阈值: 0.7")
        
        result2 = highlighter2.process()
        if result2['success']:
            print(f"  ✅ 成功，添加 {result2['total_highlights']} 个高亮")
        
        # 测试3：向后兼容性
        print("\n测试3 - 向后兼容性:")
        highlighter3 = MultiKeywordHighlighter(str(test_file))
        
        id5 = highlighter3.add_keyword("审核", color="red", context_phrase="添加以供审核")  # 默认1.0阈值
        id6 = highlighter3.add_keyword("应用", color="green")  # 无上下文
        
        print(f"  关键词 {id5} 使用默认阈值: {highlighter3.global_similarity_threshold}")
        print(f"  关键词 {id6} 无上下文限制")
        
        result3 = highlighter3.process()
        if result3['success']:
            print(f"  ✅ 成功，添加 {result3['total_highlights']} 个高亮")
        
        print("\n✅ 编程API相似度匹配测试完成")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")


if __name__ == "__main__":
    test_similarity_matching()
    test_api_similarity_matching()
    
    print("\n" + "="*70)
    print("相似度匹配功能特点:")
    print("1. ✅ 支持全局和局部相似度阈值设置")
    print("2. ✅ 处理PDF文本提取中的格式差异")
    print("3. ✅ 支持多字/少字、多空格/少空格的情况")
    print("4. ✅ 完全向后兼容现有配置")
    print("5. ✅ 提供详细的匹配日志信息")
    print("6. ✅ 灵活的相似度算法（编辑距离）")
    print("="*70)
