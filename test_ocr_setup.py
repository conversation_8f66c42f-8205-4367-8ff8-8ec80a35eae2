#!/usr/bin/env python3
"""
OCR功能安装和测试脚本

检查OCR依赖是否正确安装，并提供安装指导。
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def check_python_dependencies():
    """检查Python依赖"""
    print("=== 检查Python依赖 ===")
    
    try:
        import pytesseract
        print("✓ pytesseract 已安装")
        
        try:
            from PIL import Image
            print("✓ Pillow (PIL) 已安装")
            return True
        except ImportError:
            print("✗ Pillow (PIL) 未安装")
            print("请运行: uv add Pillow")
            return False
            
    except ImportError:
        print("✗ pytesseract 未安装")
        print("请运行: uv add pytesseract Pillow")
        return False

def check_tesseract_engine():
    """检查Tesseract OCR引擎"""
    print("\n=== 检查Tesseract OCR引擎 ===")
    
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract版本: {version}")
        return True
    except Exception as e:
        print(f"✗ Tesseract OCR引擎未正确安装: {e}")
        print("\n安装指导:")
        print("Windows:")
        print("1. 下载: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. 安装时选择中文语言包")
        print("3. 将安装目录添加到PATH环境变量")
        print("\nmacOS:")
        print("brew install tesseract tesseract-lang")
        print("\nUbuntu/Debian:")
        print("sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim")
        return False

def check_language_packs():
    """检查语言包"""
    print("\n=== 检查语言包 ===")
    
    try:
        import pytesseract
        languages = pytesseract.get_languages()
        print(f"可用语言包: {', '.join(languages)}")
        
        if 'chi_sim' in languages:
            print("✓ 中文简体语言包已安装")
        else:
            print("⚠ 中文简体语言包未安装")
            print("请安装tessdata-chi_sim语言包")
        
        if 'eng' in languages:
            print("✓ 英文语言包已安装")
        else:
            print("⚠ 英文语言包未安装")
            
        return True
    except Exception as e:
        print(f"✗ 无法检查语言包: {e}")
        return False

def test_ocr_module():
    """测试OCR模块导入"""
    print("\n=== 测试OCR模块 ===")
    
    try:
        from pdf_highlighter.core.ocr_processor import OCRProcessor, OCR_AVAILABLE
        
        if OCR_AVAILABLE:
            print("✓ OCR模块可用")
            
            # 尝试创建OCR处理器
            try:
                processor = OCRProcessor()
                print("✓ OCR处理器创建成功")
                
                # 检查缓存功能
                stats = processor.get_cache_stats()
                print(f"✓ 缓存功能正常: {stats}")
                
                return True
            except Exception as e:
                print(f"✗ OCR处理器创建失败: {e}")
                return False
        else:
            print("✗ OCR模块不可用")
            return False
            
    except ImportError as e:
        print(f"✗ 无法导入OCR模块: {e}")
        return False

def test_pdf_highlighter_ocr():
    """测试PDFHighlighter的OCR集成"""
    print("\n=== 测试PDFHighlighter OCR集成 ===")
    
    try:
        from pdf_highlighter import PDFHighlighter
        
        # 检查是否有测试PDF文件
        test_files = ["test.pdf", "test2.pdf"]
        test_file = None
        
        for file in test_files:
            if os.path.exists(file):
                test_file = file
                break
        
        if not test_file:
            print("⚠ 没有找到测试PDF文件")
            print("请准备一个PDF文件进行测试")
            return False
        
        print(f"使用测试文件: {test_file}")
        
        # 测试OCR功能集成
        try:
            with PDFHighlighter(test_file, enable_ocr=True) as highlighter:
                print("✓ PDFHighlighter OCR模式创建成功")
                
                # 测试OCR搜索方法是否存在
                if hasattr(highlighter, 'search_in_images_only'):
                    print("✓ OCR搜索方法已集成")
                else:
                    print("✗ OCR搜索方法未找到")
                    return False
                
                return True
                
        except Exception as e:
            print(f"✗ PDFHighlighter OCR测试失败: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ 无法导入PDFHighlighter: {e}")
        return False

def main():
    """主函数"""
    print("PDF OCR功能安装检查")
    print("=" * 50)
    
    # 检查各个组件
    python_deps_ok = check_python_dependencies()
    tesseract_ok = check_tesseract_engine()
    lang_packs_ok = check_language_packs() if tesseract_ok else False
    ocr_module_ok = test_ocr_module() if python_deps_ok else False
    integration_ok = test_pdf_highlighter_ocr() if python_deps_ok else False
    
    print("\n" + "=" * 50)
    print("检查结果汇总:")
    print(f"Python依赖: {'✓' if python_deps_ok else '✗'}")
    print(f"Tesseract引擎: {'✓' if tesseract_ok else '✗'}")
    print(f"语言包: {'✓' if lang_packs_ok else '✗'}")
    print(f"OCR模块: {'✓' if ocr_module_ok else '✗'}")
    print(f"功能集成: {'✓' if integration_ok else '✗'}")
    
    if all([python_deps_ok, tesseract_ok, lang_packs_ok, ocr_module_ok, integration_ok]):
        print("\n🎉 OCR功能完全可用！")
        print("您可以使用以下命令测试:")
        print("uv run pdf-highlight test.pdf '关键词' --enable-ocr")
    else:
        print("\n⚠ OCR功能未完全配置")
        print("请按照上述指导完成安装")
        
        if python_deps_ok and not tesseract_ok:
            print("\n主要问题: Tesseract OCR引擎未安装")
            print("这是最常见的问题，请按照上述指导安装Tesseract")

if __name__ == "__main__":
    main()
