#!/usr/bin/env python3
"""
错误处理和降级策略测试脚本

测试当配置文件中的上下文短语不包含指定关键字时的处理机制：
1. 验证系统不会因配置错误而中断
2. 测试降级处理策略（忽略上下文配置，按普通搜索处理）
3. 验证警告日志的正确输出
4. 确保系统的健壮性
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置详细日志以观察错误处理
logging.basicConfig(level=logging.DEBUG)
logger = get_logger()


def create_error_test_config():
    """创建包含错误配置的测试文件"""
    config = {
        "keywords": [
            {
                "text": "审核",
                "color": "red",
                "opacity": 0.8,
                "pages": "3",
                "context_phrase": "添加以供1审2核",  # 错误：关键字被数字分隔
                "priority": 1
            },
            {
                "text": "提交",
                "color": "blue",
                "opacity": 0.7,
                "context_phrase": "点击确认按钮",  # 错误：上下文中没有"提交"
                "priority": 1
            },
            {
                "text": "保存",
                "color": "green",
                "opacity": 0.6,
                "context_phrase": "",  # 错误：空的上下文短语
                "priority": 1
            },
            {
                "text": "删除",
                "color": "orange",
                "opacity": 0.5,
                "context_phrase": None,  # 正常：null上下文
                "priority": 1
            },
            {
                "text": "正常关键词",
                "color": "yellow",
                "opacity": 0.4,
                "priority": 1
            },
            {
                "text": "应用",
                "color": "purple",
                "opacity": 0.6,
                "context_phrase": "苹果ipa应用",  # 正常：包含关键字
                "priority": 1
            }
        ],
        "global_options": {
            "output_suffix": "_error_test"
        }
    }
    
    config_file = project_root / "examples" / "error_test_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"错误测试配置文件已创建: {config_file}")
    return config_file


def test_error_handling():
    """测试错误处理和降级策略"""
    print("=== 错误处理和降级策略测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 测试1：配置文件方式测试错误处理
        print("\n=== 测试1：配置文件错误处理 ===")
        config_file = create_error_test_config()
        
        highlighter1 = MultiKeywordHighlighter(str(test_file))
        
        print("加载包含错误配置的文件...")
        highlighter1.load_config_file(str(config_file))
        
        print("\n配置加载结果:")
        for i, config in enumerate(highlighter1.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文)"
            print(f"  {i}. '{config.text}' -> {config.color}{context_info}")
        
        # 执行处理
        output_file1 = project_root / f"error_test_config_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file1}")
        
        result1 = highlighter1.process(str(output_file1))
        
        if result1['success']:
            print(f"✅ 错误处理测试成功！")
            print(f"总共添加 {result1['total_highlights']} 个高亮")
            print("\n各关键词处理结果:")
            for keyword, info in result1['keywords_processed'].items():
                print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
        else:
            print(f"❌ 处理失败: {result1['error']}")
        
        # 测试2：编程API方式测试错误处理
        print(f"\n=== 测试2：编程API错误处理 ===")
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        
        print("添加包含错误的关键词配置:")
        
        # 错误配置1：关键字不在上下文中
        highlighter2.add_keyword("审核", color="red", context_phrase="添加以供1审2核")
        
        # 错误配置2：空的上下文短语
        highlighter2.add_keyword("提交", color="blue", context_phrase="")
        
        # 正常配置
        highlighter2.add_keyword("应用", color="green", context_phrase="苹果ipa应用")
        highlighter2.add_keyword("普通关键词", color="yellow")
        
        print("\n配置结果:")
        for i, config in enumerate(highlighter2.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文)"
            print(f"  {i}. '{config.text}' -> {config.color}{context_info}")
        
        output_file2 = project_root / f"error_test_api_{test_file.name}"
        result2 = highlighter2.process(str(output_file2))
        
        if result2['success']:
            print(f"✅ API错误处理测试成功！")
            print(f"总共添加 {result2['total_highlights']} 个高亮")
        else:
            print(f"❌ API测试失败: {result2['error']}")
        
        # 测试3：极端错误情况
        print(f"\n=== 测试3：极端错误情况 ===")
        highlighter3 = MultiKeywordHighlighter(str(test_file))
        
        # 添加各种边界情况
        highlighter3.add_keyword("", color="red")  # 空关键字
        highlighter3.add_keyword("测试", color="blue", context_phrase=None)  # None上下文
        highlighter3.add_keyword("正常", color="green")
        
        try:
            result3 = highlighter3.process()
            print(f"✅ 极端情况处理成功")
        except Exception as e:
            print(f"⚠️ 极端情况处理异常: {e}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 错误处理和降级策略测试完成！")
    return True


def test_specific_error_cases():
    """测试特定的错误情况"""
    print("\n=== 特定错误情况测试 ===")
    
    test_cases = [
        {
            "name": "关键字被数字分隔",
            "text": "审核",
            "context_phrase": "添加以供1审2核"
        },
        {
            "name": "关键字被特殊字符分隔", 
            "text": "提交",
            "context_phrase": "点击-提@交-按钮"
        },
        {
            "name": "关键字不存在",
            "text": "删除",
            "context_phrase": "保存文件操作"
        },
        {
            "name": "空上下文短语",
            "text": "保存",
            "context_phrase": ""
        },
        {
            "name": "正常情况",
            "text": "应用",
            "context_phrase": "苹果ipa应用"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        print(f"  关键字: '{case['text']}'")
        print(f"  上下文: '{case['context_phrase']}'")
        
        try:
            from pdf_highlighter.core.multi_highlighter import KeywordConfig
            config = KeywordConfig(
                text=case['text'],
                context_phrase=case['context_phrase']
            )
            
            if config.context_phrase:
                print(f"  结果: ✅ 配置有效，上下文: '{config.context_phrase}'")
            else:
                print(f"  结果: ⚠️ 上下文配置被清除，降级为普通搜索")
                
        except Exception as e:
            print(f"  结果: ❌ 配置失败: {e}")


if __name__ == "__main__":
    # 运行测试
    test_error_handling()
    test_specific_error_cases()
    
    print("\n" + "="*70)
    print("错误处理机制总结:")
    print("1. ✅ 配置验证时发现错误不会中断程序")
    print("2. ✅ 无效的上下文配置会被自动清除")
    print("3. ✅ 系统会记录详细的警告日志")
    print("4. ✅ 自动降级为普通关键字搜索")
    print("5. ✅ 保证系统的健壮性和可用性")
    print("6. ✅ 提供清晰的错误提示信息")
    print("="*70)
