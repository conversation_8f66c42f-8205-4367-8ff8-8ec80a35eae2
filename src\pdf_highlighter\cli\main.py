"""
命令行主程序入口

集成所有核心功能模块，实现完整的PDF关键词高亮工作流程。
"""

import sys
import time
from typing import Optional
import pymupdf

from pdf_highlighter.cli.args import parse_args, validate_parsed_args, print_args_summary
from pdf_highlighter.core.highlighter import PDFHighlighter
from pdf_highlighter.core.multi_highlighter import MultiKeywordHighlighter, create_sample_multi_config
from pdf_highlighter.core.searcher import TextSearcher
from pdf_highlighter.core.validator import ParameterValidator


def main() -> int:
    """主程序入口点

    Returns:
        int: 程序退出码，0表示成功，非0表示失败
    """
    try:
        # 解析和验证命令行参数
        args = parse_args()

        # 处理创建配置文件请求
        if hasattr(args, 'create_config') and args.create_config:
            config_path = create_sample_multi_config(args.create_config)
            print(f"示例配置文件已创建: {config_path}")
            return 0

        args = validate_parsed_args(args)

        # 显示参数摘要（如果启用详细输出）
        if args.verbose:
            print_args_summary(args)
            print()

        # 根据模式选择处理方式
        if args.config:
            return process_multi_keywords_config(args)
        elif hasattr(args, 'keywords') and args.keywords:
            return process_multi_keywords_cli(args)
        else:
            return process_single_keyword(args)

    except KeyboardInterrupt:
        print("\n操作被用户中断", file=sys.stderr)
        return 130
    except Exception as e:
        print(f"程序执行出错: {e}", file=sys.stderr)
        if 'args' in locals() and hasattr(args, 'verbose') and args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def process_single_keyword(args) -> int:
    """处理PDF文件
    
    Args:
        args: 命令行参数对象
        
    Returns:
        int: 处理结果，0表示成功
    """
    start_time = time.time()
    
    try:
        # 打开PDF文件
        if args.verbose:
            print(f"正在打开PDF文件: {args.input_file}")
        
        # 确定是否启用OCR
        enable_ocr = getattr(args, 'enable_ocr', False) or getattr(args, 'ocr_only', False)

        with PDFHighlighter(args.input_file, enable_ocr=enable_ocr) as highlighter:
            # 获取PDF信息
            total_pages = highlighter.get_page_count()
            if args.verbose:
                print(f"PDF文件信息: 共{total_pages}页")
            
            # 验证和解析页码范围
            if args.pages:
                pages = ParameterValidator.validate_pages(args.pages, total_pages)
                if args.verbose:
                    print(f"搜索页面: {pages}")
            else:
                pages = None
                if args.verbose:
                    print("搜索页面: 所有页面")
            
            # 设置OCR配置
            if enable_ocr:
                from pdf_highlighter.config.settings import set_config
                if hasattr(args, 'ocr_lang') and args.ocr_lang:
                    set_config('ocr.language', args.ocr_lang)
                if hasattr(args, 'ocr_confidence') and args.ocr_confidence:
                    set_config('ocr.confidence_threshold', args.ocr_confidence)

            # 执行搜索
            if args.verbose:
                search_mode = "OCR图片搜索" if getattr(args, 'ocr_only', False) else "文本搜索"
                if enable_ocr and not getattr(args, 'ocr_only', False):
                    search_mode += " + OCR图片搜索"
                print(f"正在执行{search_mode}: '{args.keyword}'")

            if getattr(args, 'ocr_only', False):
                # 仅搜索图片中的文字
                search_results = highlighter.search_in_images_only(
                    args.keyword,
                    pages=pages,
                    case_sensitive=args.case_sensitive,
                    regex=args.regex
                )
            else:
                # 正常搜索（可能包含OCR）
                search_results = highlighter.search_keywords(
                    args.keyword,
                    pages=pages,
                    case_sensitive=args.case_sensitive,
                    regex=args.regex,
                    enable_ocr=enable_ocr
                )
            
            # 统计搜索结果
            total_matches = sum(len(quads) for _, quads in search_results)
            pages_with_matches = len(search_results)
            
            if total_matches == 0:
                print(f"未找到关键词 '{args.keyword}' 的匹配项")
                return 0
            
            print(f"找到 {total_matches} 个匹配项，分布在 {pages_with_matches} 页中")
            
            # 显示详细搜索结果
            if args.verbose:
                print("\n搜索结果详情:")
                for page_num, quads in search_results:
                    print(f"  第{page_num}页: {len(quads)}个匹配项")
            
            # 验证关键词序号
            if args.occurrence:
                occurrence = ParameterValidator.validate_occurrence(args.occurrence, total_matches)
                if args.verbose:
                    print(f"将高亮第 {occurrence} 个匹配项")
            else:
                occurrence = None
                if args.verbose:
                    print("将高亮所有匹配项")
            
            # 预览模式：只显示结果，不生成文件
            if args.dry_run:
                print("\n=== 预览模式 ===")
                print("如果执行高亮操作，将会:")
                highlight_count = occurrence if occurrence else total_matches
                print(f"- 高亮 {highlight_count} 个匹配项")
                print(f"- 使用颜色: {args.color}")
                print(f"- 透明度: {args.opacity}")
                
                # 生成输出文件路径用于显示
                output_path = ParameterValidator.validate_output_path(
                    args.output_file or '', args.input_file
                )
                print(f"- 保存到: {output_path}")
                print("\n使用 --dry-run 参数只预览，不会生成实际文件")
                return 0
            
            # 添加高亮注释
            if args.verbose:
                print(f"正在添加高亮注释...")
            
            highlight_count = highlighter.add_highlights(
                search_results,
                color=args.color,
                opacity=args.opacity,
                occurrence=occurrence
            )
            
            print(f"成功添加 {highlight_count} 个高亮注释")
            
            # 保存输出文件
            output_path = ParameterValidator.validate_output_path(
                args.output_file or '', args.input_file
            )
            
            if args.verbose:
                print(f"正在保存到: {output_path}")
            
            highlighter.save_pdf(output_path)
            
            # 显示完成信息
            elapsed_time = time.time() - start_time
            print(f"处理完成! 输出文件: {output_path}")
            
            if args.verbose:
                print(f"处理耗时: {elapsed_time:.2f}秒")
                
                # 显示文件大小信息
                import os
                input_size = os.path.getsize(args.input_file)
                output_size = os.path.getsize(output_path)
                print(f"输入文件大小: {input_size:,} 字节")
                print(f"输出文件大小: {output_size:,} 字节")
        
        return 0
        
    except Exception as e:
        print(f"处理PDF文件时出错: {e}", file=sys.stderr)
        return 1


def show_search_context(args, search_results) -> None:
    """显示搜索上下文（详细模式）
    
    Args:
        args: 命令行参数
        search_results: 搜索结果
    """
    if not args.verbose:
        return
    
    print("\n=== 搜索上下文 ===")
    
    try:
        doc = pymupdf.open(args.input_file)
        searcher = TextSearcher(doc)
        
        for page_num, quads in search_results[:3]:  # 只显示前3页的上下文
            print(f"\n第{page_num}页上下文:")
            context = searcher.highlight_search_context(page_num, args.keyword, context_lines=1)
            
            # 限制上下文长度
            if len(context) > 300:
                context = context[:300] + "..."
            
            print(context)
        
        if len(search_results) > 3:
            print(f"\n... 还有 {len(search_results) - 3} 页包含匹配项")
        
        doc.close()
        
    except Exception as e:
        print(f"获取搜索上下文时出错: {e}")


def process_multi_keywords_config(args) -> int:
    """使用配置文件处理多关键词高亮

    Args:
        args: 命令行参数

    Returns:
        int: 程序退出码
    """
    try:
        # 创建多关键词高亮处理器
        multi_highlighter = MultiKeywordHighlighter(args.input_file)

        # 加载配置文件
        multi_highlighter.load_config_file(args.config)

        # 显示配置摘要
        if args.verbose:
            config_summary = multi_highlighter.get_config_summary()
            print("配置摘要:")
            print(f"  PDF文件: {config_summary['pdf_file']}")
            print(f"  关键词数量: {config_summary['keyword_count']}")
            for i, keyword_info in enumerate(config_summary['keywords'], 1):
                print(f"  {i}. '{keyword_info['text']}' -> {keyword_info['color']} (透明度: {keyword_info['opacity']})")
            print()

        # 处理页码参数
        pages = None
        if args.pages:
            pages = ParameterValidator.parse_pages(args.pages)

        # 预览模式
        if args.dry_run:
            print("=== 预览模式（配置文件） ===")
            config_summary = multi_highlighter.get_config_summary()
            print(f"将处理 {config_summary['keyword_count']} 个关键词:")
            for keyword_info in config_summary['keywords']:
                print(f"  - '{keyword_info['text']}' -> {keyword_info['color']}")
            print(f"输出文件: {getattr(args, 'output', None) or '自动生成'}")
            print("\n使用 --dry-run 参数只预览，不会生成实际文件")
            return 0

        # 执行处理
        result = multi_highlighter.process(getattr(args, 'output', None), pages)

        if result['success']:
            print(f"处理完成！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print(f"输出文件: {result['output_file']}")

            if args.verbose:
                print("\n关键词处理详情:")
                for keyword, info in result['keywords_processed'].items():
                    print(f"  '{keyword}': {info['matches_found']} 个匹配项 -> {info['highlights_added']} 个高亮")
        else:
            print(f"处理失败: {result['error']}")
            return 1

        return 0

    except Exception as e:
        print(f"多关键词处理失败: {e}")
        return 1


def process_multi_keywords_cli(args) -> int:
    """使用命令行参数处理多关键词高亮

    Args:
        args: 命令行参数

    Returns:
        int: 程序退出码
    """
    try:
        # 创建多关键词高亮处理器
        multi_highlighter = MultiKeywordHighlighter(args.input_file)

        # 添加关键词配置
        colors = args.colors if hasattr(args, 'colors') and args.colors else None
        default_colors = ['yellow', 'blue', 'green', 'orange', 'purple', 'red', 'cyan']

        for i, keyword in enumerate(args.keywords):
            color = colors[i] if colors and i < len(colors) else default_colors[i % len(default_colors)]
            multi_highlighter.add_keyword(
                text=keyword,
                color=color,
                opacity=args.opacity,
                case_sensitive=args.case_sensitive,
                regex=args.regex
            )

        # 显示配置摘要
        if args.verbose:
            config_summary = multi_highlighter.get_config_summary()
            print("多关键词配置:")
            for i, keyword_info in enumerate(config_summary['keywords'], 1):
                print(f"  {i}. '{keyword_info['text']}' -> {keyword_info['color']}")
            print()

        # 处理页码参数
        pages = None
        if args.pages:
            pages = ParameterValidator.parse_pages(args.pages)

        # 预览模式
        if args.dry_run:
            print("=== 预览模式（多关键词） ===")
            print(f"将处理 {len(args.keywords)} 个关键词:")
            for i, keyword in enumerate(args.keywords):
                color = colors[i] if colors and i < len(colors) else default_colors[i % len(default_colors)]
                print(f"  - '{keyword}' -> {color}")
            print(f"输出文件: {getattr(args, 'output', None) or '自动生成'}")
            print("\n使用 --dry-run 参数只预览，不会生成实际文件")
            return 0

        # 执行处理
        result = multi_highlighter.process(getattr(args, 'output', None), pages)

        if result['success']:
            print(f"处理完成！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print(f"输出文件: {result['output_file']}")

            if args.verbose:
                print("\n关键词处理详情:")
                for keyword, info in result['keywords_processed'].items():
                    print(f"  '{keyword}': {info['matches_found']} 个匹配项 -> {info['highlights_added']} 个高亮")
        else:
            print(f"处理失败: {result['error']}")
            return 1

        return 0

    except Exception as e:
        print(f"多关键词处理失败: {e}")
        return 1


def print_usage_tips() -> None:
    """打印使用提示"""
    print("""
使用提示:
1. 基本用法: pdf-highlight input.pdf "关键词"
2. 指定输出: pdf-highlight input.pdf "关键词" -o output.pdf
3. 页码范围: pdf-highlight input.pdf "关键词" -p 1,3-5
4. 正则搜索: pdf-highlight input.pdf "模式" --regex
5. 预览模式: pdf-highlight input.pdf "关键词" --dry-run
6. 详细输出: pdf-highlight input.pdf "关键词" -v
7. 多关键词: pdf-highlight input.pdf --keywords "词1" "词2" --colors yellow blue
8. 配置文件: pdf-highlight input.pdf --config config.json
9. 创建配置: pdf-highlight --create-config sample.json
10. OCR搜索: pdf-highlight input.pdf "图片文字" --enable-ocr
11. 仅OCR搜索: pdf-highlight input.pdf "图片文字" --ocr-only
12. OCR语言: pdf-highlight input.pdf "中文" --enable-ocr --ocr-lang chi_sim

更多帮助请使用: pdf-highlight --help
    """)


if __name__ == "__main__":
    sys.exit(main())
