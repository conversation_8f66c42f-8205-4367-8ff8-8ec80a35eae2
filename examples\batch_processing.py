#!/usr/bin/env python3
"""
PDF高亮工具批处理示例

演示如何批量处理多个PDF文件，适用于大规模文档处理场景。
"""

import os
import sys
import argparse
import json
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import PDFHighlighter, get_logger, setup_logging


class BatchProcessor:
    """批处理器
    
    支持多线程并行处理多个PDF文件。
    """
    
    def __init__(self, max_workers: int = 4, log_file: str = None):
        """初始化批处理器
        
        Args:
            max_workers: 最大工作线程数
            log_file: 日志文件路径
        """
        self.max_workers = max_workers
        self.logger = setup_logging(log_file=log_file, level='INFO')
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_highlights': 0,
            'skipped_files': 0
        }
    
    def find_pdf_files(self, directory: str, recursive: bool = True) -> List[str]:
        """查找目录中的PDF文件
        
        Args:
            directory: 搜索目录
            recursive: 是否递归搜索子目录
            
        Returns:
            List[str]: PDF文件路径列表
        """
        pdf_files = []
        directory = Path(directory)
        
        if not directory.exists():
            self.logger.error(f"目录不存在: {directory}")
            return pdf_files
        
        if recursive:
            pattern = "**/*.pdf"
        else:
            pattern = "*.pdf"
        
        for pdf_file in directory.glob(pattern):
            if pdf_file.is_file():
                pdf_files.append(str(pdf_file))
        
        self.logger.info(f"在 {directory} 中找到 {len(pdf_files)} 个PDF文件")
        return pdf_files
    
    def process_single_file(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个PDF文件
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            Dict: 处理结果
        """
        input_path = file_info['input_path']
        output_path = file_info['output_path']
        keywords = file_info['keywords']
        options = file_info.get('options', {})
        
        result = {
            'input_path': input_path,
            'output_path': output_path,
            'success': False,
            'highlights_added': 0,
            'keywords_found': {},
            'error': None,
            'skipped': False
        }
        
        try:
            # 检查输出文件是否已存在
            if Path(output_path).exists() and not options.get('overwrite', False):
                result['skipped'] = True
                result['error'] = "输出文件已存在"
                self.logger.info(f"跳过文件（已存在）: {output_path}")
                return result
            
            # 处理PDF文件
            with PDFHighlighter(input_path) as highlighter:
                total_highlights = 0
                
                for keyword in keywords:
                    # 搜索关键词
                    search_results = highlighter.search_keywords(
                        keyword,
                        pages=options.get('pages'),
                        case_sensitive=options.get('case_sensitive', False),
                        regex=options.get('regex', False)
                    )
                    
                    if search_results:
                        matches = sum(len(quads) for _, quads in search_results)
                        result['keywords_found'][keyword] = matches
                        
                        # 添加高亮
                        count = highlighter.add_highlights(
                            search_results,
                            color=options.get('color', (1, 1, 0)),
                            opacity=options.get('opacity', 0.5),
                            occurrence=options.get('occurrence')
                        )
                        
                        total_highlights += count
                    else:
                        result['keywords_found'][keyword] = 0
                
                # 保存文件
                if total_highlights > 0:
                    # 确保输出目录存在
                    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                    
                    highlighter.save_pdf(output_path)
                    result['highlights_added'] = total_highlights
                    result['success'] = True
                    self.logger.info(f"处理完成: {input_path} -> {output_path}")
                else:
                    result['error'] = "未找到任何匹配项"
                    self.logger.warning(f"未找到匹配项: {input_path}")
        
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"处理失败 {input_path}: {e}")
        
        return result
    
    def batch_process(self, file_list: List[str], keywords: List[str], 
                     output_dir: str, **options) -> List[Dict[str, Any]]:
        """批量处理PDF文件
        
        Args:
            file_list: PDF文件路径列表
            keywords: 关键词列表
            output_dir: 输出目录
            **options: 处理选项
            
        Returns:
            List[Dict]: 处理结果列表
        """
        self.stats['total_files'] = len(file_list)
        self.logger.info(f"开始批量处理 {len(file_list)} 个文件")
        
        # 准备文件信息
        file_infos = []
        for input_path in file_list:
            input_file = Path(input_path)
            output_file = Path(output_dir) / f"{input_file.stem}_highlighted{input_file.suffix}"
            
            file_infos.append({
                'input_path': str(input_path),
                'output_path': str(output_file),
                'keywords': keywords,
                'options': options
            })
        
        # 并行处理
        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_file = {
                executor.submit(self.process_single_file, file_info): file_info
                for file_info in file_infos
            }
            
            # 收集结果
            for future in as_completed(future_to_file):
                result = future.result()
                results.append(result)
                
                # 更新统计
                if result['skipped']:
                    self.stats['skipped_files'] += 1
                elif result['success']:
                    self.stats['processed_files'] += 1
                    self.stats['total_highlights'] += result['highlights_added']
                else:
                    self.stats['failed_files'] += 1
                
                # 显示进度
                completed = len(results)
                if completed % 10 == 0 or completed == len(file_list):
                    self.logger.info(f"进度: {completed}/{len(file_list)}")
        
        self._print_summary(results)
        return results
    
    def _print_summary(self, results: List[Dict[str, Any]]) -> None:
        """打印处理摘要"""
        print("\n" + "=" * 60)
        print("批量处理摘要")
        print("=" * 60)
        print(f"总文件数: {self.stats['total_files']}")
        print(f"成功处理: {self.stats['processed_files']}")
        print(f"处理失败: {self.stats['failed_files']}")
        print(f"跳过文件: {self.stats['skipped_files']}")
        print(f"总高亮数: {self.stats['total_highlights']}")
        
        # 显示失败文件
        failed_files = [r for r in results if not r['success'] and not r['skipped']]
        if failed_files:
            print(f"\n失败文件 ({len(failed_files)}):")
            for result in failed_files[:10]:  # 只显示前10个
                print(f"  {result['input_path']}: {result['error']}")
            if len(failed_files) > 10:
                print(f"  ... 还有 {len(failed_files) - 10} 个失败文件")
    
    def save_report(self, results: List[Dict[str, Any]], report_file: str) -> None:
        """保存处理报告
        
        Args:
            results: 处理结果列表
            report_file: 报告文件路径
        """
        report = {
            'summary': self.stats,
            'results': results
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"处理报告已保存: {report_file}")


def create_sample_config():
    """创建示例配置文件"""
    config = {
        "keywords": ["关键词1", "关键词2", "重要"],
        "options": {
            "color": "yellow",
            "opacity": 0.5,
            "case_sensitive": False,
            "regex": False,
            "overwrite": False
        },
        "processing": {
            "max_workers": 4,
            "recursive": True
        }
    }
    
    config_file = project_root / "examples" / "batch_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"示例配置文件已创建: {config_file}")
    return config_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PDF批量高亮处理工具")
    parser.add_argument("input_dir", help="输入目录")
    parser.add_argument("output_dir", help="输出目录")
    parser.add_argument("--keywords", nargs="+", default=["前端", "工程师"], 
                       help="关键词列表")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--workers", type=int, default=4, help="工作线程数")
    parser.add_argument("--recursive", action="store_true", help="递归搜索子目录")
    parser.add_argument("--overwrite", action="store_true", help="覆盖已存在的文件")
    parser.add_argument("--log-file", help="日志文件路径")
    parser.add_argument("--report", help="处理报告文件路径")
    
    args = parser.parse_args()
    
    # 加载配置
    config = {}
    if args.config and Path(args.config).exists():
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    # 获取参数
    keywords = config.get('keywords', args.keywords)
    options = config.get('options', {})
    processing = config.get('processing', {})
    
    # 更新选项
    if args.overwrite:
        options['overwrite'] = True
    
    max_workers = processing.get('max_workers', args.workers)
    recursive = processing.get('recursive', args.recursive)
    
    # 创建批处理器
    processor = BatchProcessor(max_workers=max_workers, log_file=args.log_file)
    
    # 查找PDF文件
    pdf_files = processor.find_pdf_files(args.input_dir, recursive=recursive)
    
    if not pdf_files:
        print("未找到PDF文件")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件")
    print(f"关键词: {keywords}")
    print(f"输出目录: {args.output_dir}")
    
    # 批量处理
    results = processor.batch_process(pdf_files, keywords, args.output_dir, **options)
    
    # 保存报告
    if args.report:
        processor.save_report(results, args.report)


if __name__ == "__main__":
    # 如果没有命令行参数，运行示例
    if len(sys.argv) == 1:
        print("PDF批量高亮处理示例")
        print("=" * 50)
        
        # 创建示例配置
        config_file = create_sample_config()
        
        # 模拟批处理（使用测试文件）
        test_file = project_root / "test.pdf"
        if test_file.exists():
            output_dir = project_root / "examples" / "batch_output"
            log_file = project_root / "examples" / "batch_processing.log"
            
            processor = BatchProcessor(max_workers=2, log_file=str(log_file))
            
            # 模拟多个文件
            file_list = [str(test_file)] * 3
            keywords = ["前端", "工程师"]
            
            results = processor.batch_process(
                file_list, keywords, str(output_dir),
                color=(1, 1, 0), opacity=0.6, overwrite=True
            )
            
            print(f"\n日志文件: {log_file}")
            print("批处理示例完成！")
        else:
            print("测试文件不存在，无法运行示例")
    else:
        main()
