{"keywords": [{"text": "第一章", "color": "yellow", "opacity": 0.6, "pages": "1-5", "priority": 1}, {"text": "第二章", "color": "blue", "opacity": 0.6, "pages": "6-10", "priority": 1}, {"text": "第三章", "color": "green", "opacity": 0.6, "pages": "11-15", "priority": 1}, {"text": "审核", "color": "red", "opacity": 0.8, "pages": "3", "context_phrase": "添加以供审核", "priority": 1}, {"text": "审核", "color": "orange", "opacity": 0.7, "pages": "2", "context_phrase": "内部审核流程", "priority": 1}, {"text": "审核", "color": "purple", "opacity": 0.6, "pages": "1,4-6", "priority": 2}, {"text": "重要概念", "color": "cyan", "opacity": 0.7, "pages": "1-3", "priority": 1}, {"text": "重要", "color": "pink", "opacity": 0.5, "pages": "1-3", "priority": 2}, {"text": "API接口文档", "color": "red", "opacity": 0.8, "priority": 1}, {"text": "API接口", "color": "blue", "opacity": 0.7, "priority": 2}, {"text": "接口", "color": "green", "opacity": 0.6, "priority": 3}, {"text": "前端|后端|全栈", "color": "orange", "opacity": 0.6, "regex": true, "case_sensitive": false}, {"text": "Python", "color": "#FF6B6B", "opacity": 0.7, "case_sensitive": true, "whole_word": true, "occurrence": 3}, {"text": "确认提交", "color": "yellow", "opacity": 0.5, "context_phrase": "请确认提交信息"}], "global_options": {"pages": null, "occurrence": null, "whole_word": false, "output_suffix": "_complete_highlighted"}}