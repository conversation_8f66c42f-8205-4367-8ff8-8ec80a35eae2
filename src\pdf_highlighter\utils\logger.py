"""
日志配置和管理模块

提供统一的日志配置和管理功能，支持不同日志级别和输出方式。
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Union


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器
    
    为不同级别的日志添加颜色，提升控制台输出的可读性。
    """
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        """格式化日志记录"""
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class PDFHighlighterLogger:
    """PDF高亮工具日志管理器
    
    提供统一的日志配置和管理功能。
    """
    
    def __init__(self, name: str = "pdf_highlighter"):
        """初始化日志管理器
        
        Args:
            name: 日志器名称
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 彩色格式化器
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        self.logger.addHandler(console_handler)
    
    def add_file_handler(self, log_file: Union[str, Path], 
                        level: int = logging.DEBUG,
                        max_bytes: int = 10 * 1024 * 1024,  # 10MB
                        backup_count: int = 5):
        """添加文件日志处理器
        
        Args:
            log_file: 日志文件路径
            level: 日志级别
            max_bytes: 单个日志文件最大大小
            backup_count: 备份文件数量
        """
        log_file = Path(log_file)
        
        # 确保日志目录存在
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_bytes, backupCount=backup_count, encoding='utf-8'
        )
        file_handler.setLevel(level)
        
        # 文件格式化器（不使用颜色）
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
    
    def set_level(self, level: Union[int, str]):
        """设置日志级别
        
        Args:
            level: 日志级别，可以是数字或字符串
        """
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        
        self.logger.setLevel(level)
        
        # 同时设置所有处理器的级别
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                # 控制台处理器
                handler.setLevel(max(level, logging.INFO))
            else:
                # 文件处理器
                handler.setLevel(level)
    
    def enable_debug_mode(self):
        """启用调试模式"""
        self.set_level(logging.DEBUG)
        
        # 为控制台处理器也启用DEBUG级别
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                handler.setLevel(logging.DEBUG)
    
    def disable_console_output(self):
        """禁用控制台输出"""
        for handler in self.logger.handlers[:]:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                self.logger.removeHandler(handler)
    
    def get_logger(self) -> logging.Logger:
        """获取日志器对象"""
        return self.logger


# 全局日志管理器实例
_logger_manager = None


def get_logger(name: str = "pdf_highlighter") -> logging.Logger:
    """获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器对象
    """
    global _logger_manager
    
    if _logger_manager is None:
        _logger_manager = PDFHighlighterLogger(name)
    
    return _logger_manager.get_logger()


def setup_logging(log_file: Optional[Union[str, Path]] = None,
                 level: Union[int, str] = logging.INFO,
                 debug: bool = False,
                 console: bool = True) -> logging.Logger:
    """设置日志配置
    
    Args:
        log_file: 日志文件路径，None表示不输出到文件
        level: 日志级别
        debug: 是否启用调试模式
        console: 是否输出到控制台
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    global _logger_manager
    
    _logger_manager = PDFHighlighterLogger()
    
    # 设置日志级别
    if debug:
        _logger_manager.enable_debug_mode()
    else:
        _logger_manager.set_level(level)
    
    # 添加文件处理器
    if log_file:
        _logger_manager.add_file_handler(log_file)
    
    # 禁用控制台输出
    if not console:
        _logger_manager.disable_console_output()
    
    return _logger_manager.get_logger()


def log_function_call(func):
    """函数调用日志装饰器
    
    自动记录函数的调用和返回。
    """
    def wrapper(*args, **kwargs):
        logger = get_logger()
        func_name = func.__name__
        
        # 记录函数调用
        logger.debug(f"调用函数: {func_name}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func_name} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func_name} 执行失败: {e}")
            raise
    
    return wrapper


def log_performance(func):
    """性能监控日志装饰器
    
    记录函数执行时间。
    """
    def wrapper(*args, **kwargs):
        logger = get_logger()
        func_name = func.__name__
        
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"函数 {func_name} 执行完成，耗时: {duration:.3f}秒")
            return result
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"函数 {func_name} 执行失败，耗时: {duration:.3f}秒，错误: {e}")
            raise
    
    return wrapper
