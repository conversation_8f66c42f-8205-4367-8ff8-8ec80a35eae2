#!/usr/bin/env python3
"""
专门测试上下文短语在PDF中不存在的情况

这个测试专注于：上下文短语本身在PDF文件中不存在的处理策略
与之前的测试不同，这里的关键字在上下文短语中是存在的，但整个上下文短语在PDF中找不到
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置详细日志
logging.basicConfig(level=logging.DEBUG)
logger = get_logger()


def test_context_phrase_not_found():
    """测试上下文短语在PDF中不存在的情况"""
    print("=== 上下文短语在PDF中不存在的处理策略测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    strategies = ["fallback_to_normal", "skip", "warn_only"]
    results = []
    
    for strategy in strategies:
        print(f"\n=== 测试策略: {strategy} ===")
        
        try:
            # 创建高亮器，设置降级策略
            highlighter = MultiKeywordHighlighter(str(test_file), context_fallback_strategy=strategy)
            
            # 添加关键词，使用在PDF中不存在的上下文短语
            # 注意：这里的关键词在上下文短语中是存在的，但整个上下文短语在PDF中不存在
            highlighter.add_keyword("审核", color="red", context_phrase="这是一个完全不存在的上下文短语包含审核")
            highlighter.add_keyword("提交", color="blue", context_phrase="另一个不存在的上下文短语包含提交")
            highlighter.add_keyword("应用", color="green", context_phrase="苹果ipa应用")  # 这个存在
            highlighter.add_keyword("普通关键词", color="yellow")  # 无上下文
            
            print(f"降级策略: {highlighter.context_fallback_strategy}")
            
            # 显示配置
            print("配置的关键词:")
            for i, config in enumerate(highlighter.keyword_configs, 1):
                context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文)"
                print(f"  {i}. '{config.text}' -> {config.color}{context_info}")
            
            # 执行处理
            output_file = project_root / f"phrase_not_found_{strategy}_{test_file.name}"
            print(f"\n开始处理，输出文件: {output_file}")
            
            result = highlighter.process(str(output_file))
            
            if result['success']:
                print(f"✅ 处理成功！")
                print(f"总共添加 {result['total_highlights']} 个高亮")
                print("\n各关键词处理结果:")
                for keyword, info in result['keywords_processed'].items():
                    print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
                
                results.append({
                    'strategy': strategy,
                    'total_highlights': result['total_highlights'],
                    'keywords_processed': list(result['keywords_processed'].keys())
                })
            else:
                print(f"❌ 处理失败: {result['error']}")
                results.append({
                    'strategy': strategy,
                    'total_highlights': 0,
                    'keywords_processed': []
                })
        
        except Exception as e:
            logger.error(f"策略 {strategy} 测试失败: {e}")
            import traceback
            traceback.print_exc()
            results.append({
                'strategy': strategy,
                'total_highlights': 0,
                'keywords_processed': []
            })
    
    # 结果对比
    print(f"\n=== 策略对比结果 ===")
    print("| 策略 | 总高亮数 | 处理的关键词 | 说明 |")
    print("|------|----------|--------------|------|")
    
    for result in results:
        strategy = result['strategy']
        highlights = result['total_highlights']
        keywords = ', '.join(result['keywords_processed'])
        
        if strategy == "fallback_to_normal":
            desc = "不存在的上下文短语降级为普通搜索"
        elif strategy == "skip":
            desc = "不存在的上下文短语关键词被跳过"
        else:
            desc = "不存在的上下文短语关键词不处理"
        
        print(f"| {strategy} | {highlights} | {keywords} | {desc} |")
    
    # 验证策略差异
    print(f"\n=== 策略效果验证 ===")
    if len(set(r['total_highlights'] for r in results)) > 1:
        print("✅ 不同策略产生了不同的结果，策略生效")
        for result in results:
            print(f"  {result['strategy']}: {result['total_highlights']} 个高亮")
    else:
        print("⚠️ 所有策略产生了相同的结果，可能需要检查实现")
    
    print("\n✅ 上下文短语在PDF中不存在的处理策略测试完成！")
    return True


def test_mixed_scenarios():
    """测试混合场景：有些上下文存在，有些不存在"""
    print(f"\n=== 混合场景测试 ===")
    
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        return
    
    test_file = test_files[0]
    
    # 测试 skip 策略的效果
    highlighter = MultiKeywordHighlighter(str(test_file), context_fallback_strategy="skip")
    
    # 添加混合的关键词配置
    highlighter.add_keyword("审核", color="red", context_phrase="这个上下文不存在包含审核")  # 不存在
    highlighter.add_keyword("应用", color="green", context_phrase="苹果ipa应用")  # 存在
    highlighter.add_keyword("提交", color="blue", context_phrase="另一个不存在的上下文包含提交")  # 不存在
    highlighter.add_keyword("普通", color="yellow")  # 无上下文
    
    print("混合场景配置:")
    for i, config in enumerate(highlighter.keyword_configs, 1):
        context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文)"
        print(f"  {i}. '{config.text}' -> {config.color}{context_info}")
    
    result = highlighter.process()
    
    if result['success']:
        print(f"\n混合场景结果:")
        print(f"总共添加 {result['total_highlights']} 个高亮")
        print("各关键词处理结果:")
        for keyword, info in result['keywords_processed'].items():
            print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
        
        # 验证 skip 策略是否生效
        processed_keywords = set(result['keywords_processed'].keys())
        expected_skipped = {"审核", "提交"}  # 这两个应该被跳过
        expected_processed = {"应用", "普通"}  # 这两个应该被处理
        
        if expected_processed.issubset(processed_keywords):
            print("✅ 存在的上下文和无上下文关键词正常处理")
        else:
            print("⚠️ 存在的上下文关键词处理异常")
        
        if expected_skipped.isdisjoint(processed_keywords):
            print("✅ 不存在的上下文关键词被正确跳过")
        else:
            print("⚠️ 不存在的上下文关键词没有被跳过")


if __name__ == "__main__":
    test_context_phrase_not_found()
    test_mixed_scenarios()
    
    print("\n" + "="*70)
    print("测试总结:")
    print("1. fallback_to_normal: 上下文短语不存在时，降级为普通关键字搜索")
    print("2. skip: 上下文短语不存在时，完全跳过该关键字")
    print("3. warn_only: 上下文短语不存在时，仅记录警告，不进行搜索")
    print("\n这些策略帮助用户在配置错误时仍能获得合理的处理结果")
    print("="*70)
