{"keywords": [{"text": "前端", "color": "yellow", "opacity": 0.6, "case_sensitive": false, "regex": false, "pages": "1,3-5"}, {"text": "后端", "color": "blue", "opacity": 0.7, "case_sensitive": false, "regex": false, "pages": "2,6-10"}, {"text": "工程师|程序员", "color": "green", "opacity": 0.5, "case_sensitive": false, "regex": true}, {"text": "开发", "color": "orange", "opacity": 0.8, "case_sensitive": false, "regex": false, "occurrence": 5, "pages": "1-3"}, {"text": "API", "color": "#FF6B6B", "opacity": 0.6, "case_sensitive": true, "regex": false, "whole_word": true, "pages": "4-6"}, {"text": "重要", "color": "red", "opacity": 0.7, "pages": "1-2"}, {"text": "重要", "color": "purple", "opacity": 0.8, "pages": "7-10"}], "global_options": {"pages": null, "occurrence": null, "whole_word": false, "output_suffix": "_config_highlighted"}}