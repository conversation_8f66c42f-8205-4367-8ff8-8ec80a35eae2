#!/usr/bin/env python3
"""
关键词ID功能测试脚本

测试新增的关键词ID功能：
1. 自动生成ID功能
2. 手动指定ID功能
3. 按ID进行结果统计
4. 详细的处理结果输出
5. 向后兼容性验证
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置详细日志
logging.basicConfig(level=logging.INFO)
logger = get_logger()


def create_id_test_config():
    """创建包含ID字段的测试配置文件"""
    config = {
        "keywords": [
            {
                "id": "custom_review",
                "text": "审核",
                "color": "red",
                "opacity": 0.8,
                "pages": "3",
                "context_phrase": "添加以供审核",
                "priority": 1
            },
            {
                "id": "custom_submit",
                "text": "提交",
                "color": "blue",
                "opacity": 0.7,
                "pages": "2",
                "priority": 1
            },
            {
                "text": "应用",
                "color": "green",
                "opacity": 0.6,
                "context_phrase": "苹果ipa应用",
                "priority": 1
            },
            {
                "id": "normal_keyword",
                "text": "普通关键词",
                "color": "yellow",
                "opacity": 0.5,
                "priority": 2
            },
            {
                "text": "不存在的关键词",
                "color": "purple",
                "opacity": 0.4,
                "priority": 3
            }
        ],
        "global_options": {
            "context_fallback_strategy": "fallback_to_normal",
            "output_suffix": "_id_test"
        }
    }
    
    config_file = project_root / "examples" / "id_test_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"ID测试配置文件已创建: {config_file}")
    return config_file


def test_keyword_id_functionality():
    """测试关键词ID功能"""
    print("=== 关键词ID功能测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 测试1：配置文件方式测试ID功能
        print("\n=== 测试1：配置文件ID功能 ===")
        config_file = create_id_test_config()
        
        highlighter1 = MultiKeywordHighlighter(str(test_file))
        highlighter1.load_config_file(str(config_file))
        
        print("配置的关键词及其ID:")
        for i, config in enumerate(highlighter1.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else ""
            pages_info = f" (页面: {config.pages})" if config.pages else ""
            print(f"  {i}. ID: {config.id} | 文本: '{config.text}' | 颜色: {config.color}{pages_info}{context_info}")
        
        # 执行处理
        output_file1 = project_root / f"id_test_config_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file1}")
        
        result1 = highlighter1.process(str(output_file1))
        
        if result1['success']:
            print(f"✅ 配置文件ID测试成功！")
            print(f"总共添加 {result1['total_highlights']} 个高亮")
            
            # 显示按ID分组的详细结果
            print("\n按ID分组的处理结果:")
            for keyword_id, stats in result1['keywords_by_id'].items():
                status_icon = "✅" if stats['highlights_added'] > 0 else "❌"
                print(f"  {status_icon} [{keyword_id}] '{stats['text']}': "
                      f"匹配 {stats['matches_found']} 个，高亮 {stats['highlights_added']} 个 "
                      f"(状态: {stats['status']})")
            
            # 显示统计摘要
            summary = result1['summary']
            print(f"\n📊 统计摘要:")
            print(f"  总关键词: {summary['total_keywords']}")
            print(f"  成功: {summary['successful_keywords']}")
            print(f"  失败: {summary['failed_keywords']}")
            print(f"  跳过: {summary['skipped_keywords']}")
            
        else:
            print(f"❌ 配置文件ID测试失败: {result1['error']}")
        
        # 测试2：编程API方式测试ID功能
        print(f"\n=== 测试2：编程API ID功能 ===")
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        
        # 添加关键词，测试ID功能
        id1 = highlighter2.add_keyword("审核", color="red", id="manual_review", context_phrase="添加以供审核")
        id2 = highlighter2.add_keyword("应用", color="green", id="manual_app", context_phrase="苹果ipa应用")
        id3 = highlighter2.add_keyword("普通", color="blue")  # 自动生成ID
        id4 = highlighter2.add_keyword("不存在", color="yellow", id="not_found")  # 不存在的关键词
        
        print("编程API添加的关键词:")
        print(f"  1. ID: {id1} | 文本: '审核' (手动指定ID)")
        print(f"  2. ID: {id2} | 文本: '应用' (手动指定ID)")
        print(f"  3. ID: {id3} | 文本: '普通' (自动生成ID)")
        print(f"  4. ID: {id4} | 文本: '不存在' (手动指定ID)")
        
        output_file2 = project_root / f"id_test_api_{test_file.name}"
        result2 = highlighter2.process(str(output_file2))
        
        if result2['success']:
            print(f"\n✅ 编程API ID测试成功！")
            print(f"总共添加 {result2['total_highlights']} 个高亮")
            
            # 验证返回的ID是否正确
            returned_ids = set(result2['keywords_by_id'].keys())
            expected_ids = {id1, id2, id3, id4}
            
            if returned_ids == expected_ids:
                print("✅ 返回的关键词ID完全匹配")
            else:
                print(f"⚠️ ID不匹配 - 期望: {expected_ids}, 实际: {returned_ids}")
        else:
            print(f"❌ 编程API ID测试失败: {result2['error']}")
        
        # 测试3：向后兼容性测试
        print(f"\n=== 测试3：向后兼容性测试 ===")
        old_config = {
            "keywords": [
                {
                    "text": "审核",
                    "color": "red",
                    "opacity": 0.8
                },
                {
                    "text": "应用",
                    "color": "green",
                    "context_phrase": "苹果ipa应用"
                }
            ]
        }
        
        old_config_file = project_root / "examples" / "backward_compatible_test.json"
        with open(old_config_file, 'w', encoding='utf-8') as f:
            json.dump(old_config, f, indent=2, ensure_ascii=False)
        
        highlighter3 = MultiKeywordHighlighter(str(test_file))
        highlighter3.load_config_file(str(old_config_file))
        
        print("向后兼容性测试 - 无ID字段的配置:")
        for config in highlighter3.keyword_configs:
            print(f"  文本: '{config.text}' | 自动生成ID: {config.id}")
        
        result3 = highlighter3.process()
        
        if result3['success']:
            print(f"✅ 向后兼容性测试成功！自动为旧配置生成了ID")
        else:
            print(f"❌ 向后兼容性测试失败")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 关键词ID功能测试完成！")
    return True


def test_id_uniqueness():
    """测试ID唯一性"""
    print(f"\n=== ID唯一性测试 ===")
    
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        return
    
    test_file = test_files[0]
    highlighter = MultiKeywordHighlighter(str(test_file))
    
    # 添加相同的关键词，测试ID生成
    id1 = highlighter.add_keyword("测试", color="red")
    id2 = highlighter.add_keyword("测试", color="blue", pages="1-2")
    id3 = highlighter.add_keyword("测试", color="green", context_phrase="这是测试")
    
    print("相同关键词的不同配置:")
    print(f"  1. ID: {id1} | 基础配置")
    print(f"  2. ID: {id2} | 带页面范围")
    print(f"  3. ID: {id3} | 带上下文短语")
    
    # 验证ID唯一性
    ids = [id1, id2, id3]
    if len(set(ids)) == len(ids):
        print("✅ ID唯一性测试通过")
    else:
        print("❌ ID唯一性测试失败，存在重复ID")


if __name__ == "__main__":
    test_keyword_id_functionality()
    test_id_uniqueness()
    
    print("\n" + "="*70)
    print("关键词ID功能特点:")
    print("1. ✅ 支持手动指定关键词ID")
    print("2. ✅ 自动生成唯一ID（基于内容哈希）")
    print("3. ✅ 按ID进行结果统计和分组")
    print("4. ✅ 详细的处理结果输出")
    print("5. ✅ 完全向后兼容现有配置")
    print("6. ✅ 支持相同关键词的不同配置")
    print("="*70)
