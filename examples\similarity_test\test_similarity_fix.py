#!/usr/bin/env python3
"""
测试相似度匹配算法修复
"""

import sys
import json
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter.core.multi_highlighter import MultiKeywordHighlighter


def test_similarity_fix():
    """测试相似度匹配算法修复"""
    print("=== 测试相似度匹配算法修复 ===\n")
    
    # 检查测试文件
    test_pdf = Path(__file__).parent / "test-file.pdf"
    config_file = Path(__file__).parent / "test_similarity_config.json"
    
    if not test_pdf.exists():
        print(f"❌ 测试PDF文件不存在: {test_pdf}")
        return False
        
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    print(f"📄 使用测试文件: {test_pdf}")
    print(f"⚙️ 使用配置文件: {config_file}")
    
    # 加载配置文件内容
    with open(config_file, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    print("\n📋 配置信息:")
    print(f"   全局相似度阈值: {config_data['global_options']['context_similarity_threshold']}")
    print("   关键词配置:")
    for i, kw in enumerate(config_data['keywords'], 1):
        print(f"     {i}. [{kw['id']}] '{kw['text']}' (阈值: {kw['similarity_threshold']})")
        print(f"        上下文: {kw['context_phrase'][:50]}...")
    
    try:
        # 创建高亮器并加载配置
        highlighter = MultiKeywordHighlighter(str(test_pdf))
        highlighter.load_config_file(str(config_file))
        
        print(f"\n🔧 全局相似度阈值: {highlighter.global_similarity_threshold}")
        print(f"📝 加载了 {len(highlighter.keyword_configs)} 个关键词配置")
        
        # 执行处理
        output_file = project_root / f"test_similarity_fixed_{test_pdf.name}"
        print(f"\n🚀 开始处理，输出文件: {output_file}")
        
        result = highlighter.process(str(output_file))
        
        # 显示结果
        print(f"\n📊 处理结果:")
        print(f"   成功: {result['success']}")
        print(f"   总高亮数: {result.get('total_highlights', 0)}")
        
        if result['success']:
            print(f"   ✅ 输出文件: {result['output_file']}")
            
            # 显示详细统计
            print(f"\n📈 详细统计:")
            for kw_id, stats in result.get('keywords_by_id', {}).items():
                status_icon = "✅" if stats['highlights_added'] > 0 else "❌"
                print(f"   {status_icon} [{stats['id']}] '{stats['text']}': {stats['highlights_added']} 个高亮")
        else:
            print(f"   ❌ 错误: {result.get('error', '未知错误')}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_similarity_fix()
    print(f"\n{'='*50}")
    if success:
        print("🎉 相似度匹配算法修复测试成功！")
        print("✅ 现在只会在相似的上下文段落中高亮关键词")
    else:
        print("❌ 相似度匹配算法修复测试失败")
    print(f"{'='*50}")
