{"description": "长关键词智能降级搜索测试配置", "keywords": [{"id": "long_keyword_intelligent_search", "text": "本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术", "color": "yellow", "opacity": 0.7, "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 4, "comment": "长关键词，启用智能降级搜索"}, {"id": "medium_keyword_test", "text": "教育技术学专业教材培训用书", "color": "blue", "opacity": 0.6, "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "comment": "中等长度关键词测试"}, {"id": "short_keyword_test", "text": "教育技术", "color": "green", "opacity": 0.8, "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "comment": "短关键词，应该能直接匹配"}], "global_options": {"fuzzy_matching_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "output_suffix": "_intelligent_fallback_test"}, "intelligent_fallback_explanation": {"purpose": "当长关键词的直接模糊匹配失败时，自动分解关键词并搜索有意义的片段", "workflow": ["1. 尝试直接模糊匹配（如：本书[\\s\\S]*?教育技术）", "2. 如果失败，分解长关键词为有意义的片段", "3. 按重要性顺序搜索各个片段", "4. 找到第一个匹配的片段即返回结果"], "fragment_extraction_strategies": ["按标点符号分割（，。、；：！？）", "提取关键模式（XX技术、XX教育、XX学习等）", "固定长度切分（开头、结尾、中间各8字符）"]}, "expected_fragments": {"long_keyword": "本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术", "extracted_parts": ["本书可以作为高校教育技术学专业教材", "大中小学教师继续教育培训用书", "也可作为教育信息化从业人员自主学习教育技术", "教育技术学专业", "教育技术", "继续教育", "专业教材", "培训用书"], "search_priority": "按片段长度降序搜索，长片段优先"}, "test_scenarios": {"scenario_1": {"description": "PDF中包含完整长关键词", "expected_behavior": "直接模糊匹配成功，不触发智能降级"}, "scenario_2": {"description": "PDF中只包含关键词的部分内容", "expected_behavior": "直接匹配失败，触发智能降级，搜索片段成功"}, "scenario_3": {"description": "PDF中包含关键词的核心概念", "expected_behavior": "通过片段搜索找到'教育技术'等核心概念"}}, "expected_logs": ["DEBUG - 关键词 '长关键词...' 使用普通搜索模式", "INFO - 长关键词 '长关键词...' 直接匹配失败，尝试智能降级搜索", "INFO - 开始长关键词智能降级搜索: '长关键词...'", "DEBUG - 从长关键词 '长关键词...' 中提取的片段: ['片段1', '片段2', ...]", "DEBUG - 搜索关键词片段: '教育技术'", "INFO - 找到关键词片段 '教育技术' 的匹配", "INFO - 长关键词智能降级搜索成功，找到 1 个匹配"], "advantages": ["无需配置context_phrase，适用于普通搜索模式", "自动分解长关键词，提高匹配成功率", "按重要性搜索片段，优先匹配最相关的内容", "保持与现有功能的完全兼容性"], "usage_tips": ["适用于长度超过20字符的关键词", "特别适合包含多个概念的复合关键词", "可以与模糊匹配功能结合使用", "建议启用详细日志以观察搜索过程"], "troubleshooting": {"if_no_fragments_found": ["检查关键词是否包含有意义的概念", "尝试手动设置更短的关键词", "考虑使用上下文匹配模式"], "if_too_many_matches": ["调整片段提取的最小长度", "使用更具体的关键词", "启用case_sensitive选项"]}}