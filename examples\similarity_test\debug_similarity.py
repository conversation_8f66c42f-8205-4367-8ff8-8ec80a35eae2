#!/usr/bin/env python3
"""
调试相似度匹配算法
"""

import sys
import difflib
import re
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

import pymupdf
from pdf_highlighter.core.multi_highlighter import MultiKeywordHighlighter


def debug_similarity_matching():
    """调试相似度匹配"""
    print("=== 调试相似度匹配算法 ===\n")
    
    # 测试文本
    test_pdf = Path(__file__).parent / "test-file.pdf"
    
    # 配置的上下文短语
    context_phrases = [
        "社会化学习可以被定义为一种认知改变，这种改变超越了个体，它通过社会网络中行动者之间的社会互动而进入更广泛的社会单元或实践社群。 (Reed,2010)",
        "当被研究者聚集起来的小组群很少形成实践社群，是因为这些小组群的成员一般来自不同的实践社群；学习成果必须要扩散到他们属于的更广泛的组群成员中去，社会化学习才能发生。"
    ]
    
    try:
        doc = pymupdf.open(str(test_pdf))
        highlighter = MultiKeywordHighlighter(str(test_pdf))
        
        print(f"📄 PDF文件: {test_pdf}")
        print(f"📖 总页数: {len(doc)}")
        
        for i, context_phrase in enumerate(context_phrases, 1):
            print(f"\n--- 测试上下文短语 {i} ---")
            print(f"配置短语: {context_phrase}")
            print(f"短语长度: {len(context_phrase)}")
            
            # 在每个页面中查找相似文本
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                
                print(f"\n页面 {page_num + 1} 文本:")
                print(page_text[:200] + "..." if len(page_text) > 200 else page_text)
                
                # 使用highlighter的方法分割文本
                phrases = highlighter._split_text_into_phrases(page_text, len(context_phrase))
                
                print(f"\n分割出的短语数量: {len(phrases)}")
                
                best_similarity = 0
                best_match = ""
                
                for phrase in phrases:
                    # 标准化文本
                    norm_context = highlighter._normalize_text(context_phrase)
                    norm_phrase = highlighter._normalize_text(phrase)
                    
                    # 计算相似度
                    similarity = difflib.SequenceMatcher(None, norm_context, norm_phrase).ratio()
                    
                    if similarity > best_similarity:
                        best_similarity = similarity
                        best_match = phrase
                    
                    # 显示相似度较高的匹配
                    if similarity > 0.3:
                        print(f"  相似度 {similarity:.3f}: {phrase[:100]}...")
                
                print(f"\n最佳匹配 (相似度 {best_similarity:.3f}): {best_match[:100]}...")
                
                if best_similarity >= 0.7:
                    print(f"✅ 找到匹配！相似度: {best_similarity:.3f}")
                else:
                    print(f"❌ 未找到匹配，最高相似度: {best_similarity:.3f}")
        
        doc.close()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_similarity_matching()
