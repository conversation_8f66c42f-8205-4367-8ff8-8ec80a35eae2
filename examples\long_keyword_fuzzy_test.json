{"description": "长关键词模糊匹配测试配置", "keywords": [{"id": "long_keyword_test", "text": "本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术", "color": "yellow", "opacity": 0.7, "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 4, "comment": "测试长关键词的模糊匹配：本书[\\s\\S]*?教育技术"}, {"id": "long_keyword_with_context", "text": "教育技术", "color": "red", "opacity": 0.8, "context_phrase": "本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术", "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "comment": "使用长文本作为上下文，搜索其中的关键词"}, {"id": "shorter_fuzzy_test", "text": "教育技术学专业教材", "color": "blue", "opacity": 0.6, "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "comment": "测试中等长度关键词：教育[\\s\\S]*?教材"}, {"id": "context_with_long_keyword", "text": "本书可以作为高校教育技术学专业教材", "color": "green", "opacity": 0.8, "context_phrase": "本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书", "fuzzy_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "comment": "上下文匹配 + 长关键词模糊匹配"}], "global_options": {"fuzzy_matching_enabled": true, "fuzzy_start_chars": 2, "fuzzy_end_chars": 2, "context_similarity_threshold": 0.8, "output_suffix": "_long_keyword_test"}, "test_scenarios": {"scenario_1": {"description": "完整长关键词匹配", "keyword": "本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术", "expected_regex": "本书[\\s\\S]*?教育技术", "should_match": ["本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术", "本书\n可以作为高校\n教育技术学专业教材、\n大中小学教师继续教育培训用书，\n也可作为教育信息化从业人员\n自主学习教育技术"]}, "scenario_2": {"description": "部分匹配场景", "keyword": "本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术", "expected_regex": "本书[\\s\\S]*?教育技术", "should_match": ["本书是一本关于教育技术的专业书籍", "本书\n介绍了现代教育技术", "本书详细阐述了教育技术"]}}, "debugging_tips": {"check_keyword_length": "确认关键词长度是否足够进行模糊匹配", "check_start_end_chars": "验证开头和结尾字符数配置是否合理", "check_regex_generation": "查看生成的正则表达式是否正确", "check_context_validation": "对于上下文匹配，确认开头和结尾字符是否存在于上下文中", "enable_debug_logging": "启用详细日志来诊断匹配过程"}, "expected_logs": ["INFO - 模糊匹配详情 - 关键词: '本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术' (长度: 60+)", "INFO -   开头 2 字符: '本书'", "INFO -   结尾 4 字符: '教育技术'", "INFO -   生成正则表达式: '本书[\\s\\S]*?教育技术'", "DEBUG - 模糊匹配验证：检查开头字符 '本书' 和结尾字符 '教育技术'"], "troubleshooting": {"if_no_match": ["检查PDF文本中是否真的包含开头字符'本书'和结尾字符'教育技术'", "确认文本提取是否正确，可能存在特殊字符或编码问题", "尝试减少start_chars或end_chars的数量", "检查是否有上下文匹配的限制"], "if_regex_not_generated": ["检查关键词长度是否足够", "确认fuzzy_enabled设置为true", "检查全局模糊匹配是否启用"]}}