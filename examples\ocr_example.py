#!/usr/bin/env python3
"""
PDF OCR高亮功能示例

演示如何使用pdf_highlighter的OCR功能来识别和高亮PDF图片中的文字。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import PDFHighlighter
from pdf_highlighter.config.settings import set_config


def basic_ocr_example():
    """基本OCR高亮示例"""
    print("=== 基本OCR高亮示例 ===")
    
    # 假设有一个包含图片的PDF文件
    input_file = "test_with_images.pdf"
    output_file = "ocr_highlighted.pdf"
    keyword = "文字"  # 要在图片中搜索的关键词
    
    try:
        # 启用OCR功能创建高亮器
        with PDFHighlighter(input_file, enable_ocr=True) as highlighter:
            print(f"正在搜索关键词: '{keyword}'")
            
            # 搜索关键词（包括图片中的文字）
            results = highlighter.search_keywords(
                keyword,
                enable_ocr=True  # 明确启用OCR
            )
            
            if results:
                total_matches = sum(len(quads) for _, quads in results)
                print(f"找到 {total_matches} 个匹配项")
                
                # 添加高亮
                highlight_count = highlighter.add_highlights(
                    results,
                    color=(1, 1, 0),  # 黄色
                    opacity=0.6
                )
                
                # 保存结果
                highlighter.save_pdf(output_file)
                print(f"已保存到: {output_file}")
                print(f"添加了 {highlight_count} 个高亮注释")
            else:
                print("未找到匹配项")
                
    except Exception as e:
        print(f"处理失败: {e}")


def ocr_only_example():
    """仅OCR搜索示例"""
    print("\n=== 仅OCR搜索示例 ===")
    
    input_file = "test_with_images.pdf"
    output_file = "ocr_only_highlighted.pdf"
    keyword = "图片"
    
    try:
        with PDFHighlighter(input_file, enable_ocr=True) as highlighter:
            print(f"仅在图片中搜索关键词: '{keyword}'")
            
            # 仅搜索图片中的文字
            results = highlighter.search_in_images_only(
                keyword,
                case_sensitive=False
            )
            
            if results:
                total_matches = sum(len(quads) for _, quads in results)
                print(f"在图片中找到 {total_matches} 个匹配项")
                
                # 使用不同颜色高亮图片中的文字
                highlight_count = highlighter.add_highlights(
                    results,
                    color=(0, 1, 0),  # 绿色
                    opacity=0.7
                )
                
                highlighter.save_pdf(output_file)
                print(f"已保存到: {output_file}")
                print(f"添加了 {highlight_count} 个高亮注释")
            else:
                print("在图片中未找到匹配项")
                
    except Exception as e:
        print(f"处理失败: {e}")


def advanced_ocr_example():
    """高级OCR配置示例"""
    print("\n=== 高级OCR配置示例 ===")
    
    input_file = "test_with_images.pdf"
    output_file = "advanced_ocr_highlighted.pdf"
    
    try:
        # 配置OCR参数
        set_config('ocr.language', 'chi_sim+eng')  # 中文简体+英文
        set_config('ocr.confidence_threshold', 70)  # 提高置信度阈值
        set_config('ocr.image_preprocessing.scale_factor', 3.0)  # 放大图片提高精度
        set_config('ocr.image_preprocessing.enable_denoising', True)  # 启用去噪
        
        with PDFHighlighter(input_file, enable_ocr=True) as highlighter:
            # 搜索中文关键词
            chinese_results = highlighter.search_keywords(
                "中文",
                enable_ocr=True,
                case_sensitive=False
            )
            
            # 搜索英文关键词
            english_results = highlighter.search_keywords(
                "English",
                enable_ocr=True,
                case_sensitive=True
            )
            
            # 使用不同颜色高亮不同语言的文字
            if chinese_results:
                chinese_count = highlighter.add_highlights(
                    chinese_results,
                    color=(1, 0, 0),  # 红色高亮中文
                    opacity=0.5
                )
                print(f"中文匹配项: {chinese_count} 个")
            
            if english_results:
                english_count = highlighter.add_highlights(
                    english_results,
                    color=(0, 0, 1),  # 蓝色高亮英文
                    opacity=0.5
                )
                print(f"英文匹配项: {english_count} 个")
            
            if chinese_results or english_results:
                highlighter.save_pdf(output_file)
                print(f"已保存到: {output_file}")
            else:
                print("未找到任何匹配项")
                
    except Exception as e:
        print(f"处理失败: {e}")


def regex_ocr_example():
    """OCR正则表达式搜索示例"""
    print("\n=== OCR正则表达式搜索示例 ===")
    
    input_file = "test_with_images.pdf"
    output_file = "regex_ocr_highlighted.pdf"
    
    try:
        with PDFHighlighter(input_file, enable_ocr=True) as highlighter:
            # 使用正则表达式搜索数字模式
            pattern = r'\d{4}-\d{2}-\d{2}'  # 日期格式 YYYY-MM-DD
            
            print(f"使用正则表达式搜索日期模式: {pattern}")
            
            results = highlighter.search_keywords(
                pattern,
                enable_ocr=True,
                regex=True
            )
            
            if results:
                total_matches = sum(len(quads) for _, quads in results)
                print(f"找到 {total_matches} 个日期匹配项")
                
                highlight_count = highlighter.add_highlights(
                    results,
                    color=(1, 0.5, 0),  # 橙色
                    opacity=0.6
                )
                
                highlighter.save_pdf(output_file)
                print(f"已保存到: {output_file}")
                print(f"添加了 {highlight_count} 个高亮注释")
            else:
                print("未找到匹配的日期格式")
                
    except Exception as e:
        print(f"处理失败: {e}")


def page_range_ocr_example():
    """指定页面范围的OCR搜索示例"""
    print("\n=== 指定页面范围OCR搜索示例 ===")
    
    input_file = "test_with_images.pdf"
    output_file = "page_range_ocr_highlighted.pdf"
    keyword = "关键词"
    
    try:
        with PDFHighlighter(input_file, enable_ocr=True) as highlighter:
            total_pages = highlighter.get_page_count()
            print(f"PDF总页数: {total_pages}")
            
            # 只在前3页搜索
            search_pages = list(range(1, min(4, total_pages + 1)))
            print(f"搜索页面: {search_pages}")
            
            results = highlighter.search_keywords(
                keyword,
                pages=search_pages,
                enable_ocr=True
            )
            
            if results:
                print("搜索结果:")
                for page_num, quads in results:
                    print(f"  第{page_num}页: {len(quads)}个匹配项")
                
                highlight_count = highlighter.add_highlights(results)
                highlighter.save_pdf(output_file)
                print(f"已保存到: {output_file}")
                print(f"添加了 {highlight_count} 个高亮注释")
            else:
                print("在指定页面范围内未找到匹配项")
                
    except Exception as e:
        print(f"处理失败: {e}")


def check_ocr_dependencies():
    """检查OCR依赖是否正确安装"""
    print("=== 检查OCR依赖 ===")
    
    try:
        import pytesseract
        from PIL import Image
        print("✓ pytesseract 和 Pillow 已安装")
        
        # 检查Tesseract可执行文件
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract版本: {version}")
        
        # 检查中文语言包
        languages = pytesseract.get_languages()
        if 'chi_sim' in languages:
            print("✓ 中文简体语言包已安装")
        else:
            print("⚠ 中文简体语言包未安装，请安装tessdata-chi_sim")
        
        if 'eng' in languages:
            print("✓ 英文语言包已安装")
        else:
            print("⚠ 英文语言包未安装")
            
        print(f"可用语言包: {', '.join(languages)}")
        
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install pdf-highlighter[ocr]")
    except Exception as e:
        print(f"✗ OCR环境检查失败: {e}")


def main():
    """主函数"""
    print("PDF OCR高亮功能示例")
    print("=" * 50)
    
    # 首先检查OCR依赖
    check_ocr_dependencies()
    print()
    
    # 检查示例文件是否存在
    test_file = "test_with_images.pdf"
    if not os.path.exists(test_file):
        print(f"⚠ 示例文件 {test_file} 不存在")
        print("请准备一个包含图片的PDF文件用于测试")
        print("或者修改示例代码中的文件路径")
        return
    
    try:
        # 运行各种示例
        basic_ocr_example()
        ocr_only_example()
        advanced_ocr_example()
        regex_ocr_example()
        page_range_ocr_example()
        
        print("\n" + "=" * 50)
        print("所有示例执行完成！")
        print("生成的文件:")
        for filename in ["ocr_highlighted.pdf", "ocr_only_highlighted.pdf", 
                        "advanced_ocr_highlighted.pdf", "regex_ocr_highlighted.pdf",
                        "page_range_ocr_highlighted.pdf"]:
            if os.path.exists(filename):
                print(f"  - {filename}")
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"\n示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
