#!/usr/bin/env python3
"""
页面级关键字配置功能演示脚本

实际演示页面级关键字高亮功能的使用
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
logger = get_logger()


def demo_page_level_highlighting():
    """演示页面级关键字高亮功能"""
    print("=== 页面级关键字配置功能演示 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件，请在项目根目录放置一个PDF文件进行测试")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 创建多关键词高亮处理器
        highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 先获取PDF页数
        from pdf_highlighter import PDFHighlighter
        with PDFHighlighter(str(test_file)) as temp_highlighter:
            total_pages = temp_highlighter.get_page_count()

        print(f"PDF总页数: {total_pages}")

        # 方式1：使用编程API添加页面级关键词
        print("\n方式1：编程API添加页面级关键词")
        # 使用更通用的关键词，更可能在PDF中找到
        if total_pages >= 2:
            highlighter.add_keyword("的", color="yellow", pages="1-2")
        else:
            highlighter.add_keyword("的", color="yellow", pages="1")

        if total_pages >= 3:
            highlighter.add_keyword("和", color="blue", pages="3")

        highlighter.add_keyword("是", color="red", pages="1")
        if total_pages >= 3:
            highlighter.add_keyword("是", color="orange", pages="3")
        highlighter.add_keyword("在", color="green")  # 没有页面限制
        
        # 显示配置
        print("配置的关键词:")
        for i, config in enumerate(highlighter.keyword_configs, 1):
            pages_info = f" (页面: {config.pages})" if config.pages else " (所有页面)"
            print(f"  {i}. '{config.text}' -> {config.color}{pages_info}")
        
        # 执行处理
        output_file = project_root / f"demo_page_level_highlighted_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file}")
        
        result = highlighter.process(str(output_file))
        
        if result['success']:
            print(f"✅ 处理成功！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print("\n各关键词处理结果:")
            for keyword, info in result['keywords_processed'].items():
                print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
        else:
            print(f"❌ 处理失败: {result['error']}")
        
        # 方式2：使用配置文件
        print(f"\n方式2：使用配置文件")
        config_file = project_root / "examples" / "simple_page_level_config.json"
        if config_file.exists():
            highlighter2 = MultiKeywordHighlighter(str(test_file))
            highlighter2.load_config_file(str(config_file))
            
            output_file2 = project_root / f"demo_config_highlighted_{test_file.name}"
            print(f"使用配置文件: {config_file}")
            print(f"输出文件: {output_file2}")
            
            result2 = highlighter2.process(str(output_file2))
            
            if result2['success']:
                print(f"✅ 配置文件处理成功！")
                print(f"总共添加 {result2['total_highlights']} 个高亮")
            else:
                print(f"❌ 配置文件处理失败: {result2['error']}")
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 页面级关键字配置功能演示完成！")
    return True


if __name__ == "__main__":
    demo_page_level_highlighting()
    
    print("\n" + "="*60)
    print("功能特点:")
    print("1. ✅ 支持为每个关键字单独配置生效的页面范围")
    print("2. ✅ 允许同一个关键字在不同页面有不同的高亮效果")
    print("3. ✅ 保持向后兼容性，现有配置无需修改")
    print("4. ✅ 支持编程API和配置文件两种方式")
    print("5. ✅ 页面范围优先级：关键字级别 > 方法参数 > 全局配置 > 所有页面")
    print("="*60)
