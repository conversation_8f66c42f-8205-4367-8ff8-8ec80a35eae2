{"description": "完整的增强配置文件示例，展示所有新功能", "version": "2.0", "keywords": [{"id": "document_review_process", "text": "审核", "color": "red", "opacity": 0.8, "pages": "1-5", "context_phrase": "添加以供审核", "priority": 1, "case_sensitive": false, "regex": false, "whole_word": false}, {"id": "general_review_mentions", "text": "审核", "color": "orange", "opacity": 0.6, "pages": "6-10", "priority": 2, "case_sensitive": false}, {"id": "submission_process", "text": "提交", "color": "blue", "opacity": 0.7, "context_phrase": "请确认无误后提交", "priority": 1, "case_sensitive": false}, {"id": "mobile_application", "text": "应用", "color": "green", "opacity": 0.6, "context_phrase": "苹果ipa应用", "priority": 1}, {"id": "programming_languages", "text": "Python|Java|JavaScript|TypeScript", "color": "purple", "opacity": 0.5, "regex": true, "case_sensitive": true, "priority": 2}, {"id": "api_references", "text": "API", "color": "#FF6B6B", "opacity": 0.6, "case_sensitive": true, "whole_word": true, "pages": "1,3,5-7", "priority": 1}, {"id": "important_concepts_early", "text": "重要概念", "color": "yellow", "opacity": 0.7, "pages": "1-3", "priority": 1}, {"id": "important_concepts_late", "text": "重要概念", "color": "cyan", "opacity": 0.7, "pages": "8-10", "priority": 1}, {"text": "自动生成ID的关键词", "color": "pink", "opacity": 0.4, "priority": 3}, {"id": "not_found_example", "text": "这个关键词不存在", "color": "gray", "opacity": 0.3, "priority": 4}], "global_options": {"context_fallback_strategy": "fallback_to_normal", "output_suffix": "_complete_enhanced"}, "expected_results": {"description": "预期的处理结果格式", "structure": {"success": "boolean - 处理是否成功", "total_highlights": "number - 总高亮数量", "keywords_by_id": {"keyword_id": {"id": "string - 关键词ID", "text": "string - 关键词文本", "matches_found": "number - 找到的匹配数", "highlights_added": "number - 成功添加的高亮数", "status": "string - 处理状态", "all_matches": [{"page_num": "number - 页码", "text_content": "string - 匹配的文本", "bbox": "array - 边界框坐标 [x0,y0,x1,y1]", "context_text": "string - 上下文文本", "match_index": "number - 匹配序号"}], "highlights_applied": "array - 成功应用的高亮", "highlights_failed": "array - 失败的高亮", "color": "array - RGB颜色值", "opacity": "number - 透明度", "pages": "string - 页面范围", "context_phrase": "string - 上下文短语", "priority": "number - 优先级"}}, "matches_summary": {"total_matches_found": "number - 总匹配数", "total_highlights_applied": "number - 总应用高亮数", "matches_by_keyword": "object - 按关键词分组的匹配数"}, "summary": {"total_keywords": "number - 总关键词数", "successful_keywords": "number - 成功关键词数", "failed_keywords": "number - 失败关键词数", "skipped_keywords": "number - 跳过关键词数"}}}, "usage_notes": ["id字段是可选的，未提供时会自动生成", "相同文本的关键词可以有不同的ID和配置", "all_matches包含所有找到的匹配项，不管是否成功高亮", "highlights_applied只包含成功应用的高亮", "highlights_failed包含失败的高亮及错误信息", "bbox坐标格式为 [x0, y0, x1, y1]，单位为点", "context_text提供匹配项周围的文本上下文", "status可能的值：success, no_matches, failed, skipped"], "api_examples": {"basic_usage": {"python": ["highlighter = MultiKeywordHighlighter('document.pdf')", "highlighter.load_config_file('config.json')", "result = highlighter.process()", "print(f'成功: {result[\"success\"]}')", "print(f'高亮数: {result[\"total_highlights\"]}')"]}, "detailed_analysis": {"python": ["# 分析每个关键词的处理结果", "for keyword_id, stats in result['keywords_by_id'].items():", "    print(f'[{keyword_id}] {stats[\"text\"]}:')", "    print(f'  匹配: {stats[\"matches_found\"]}个')", "    print(f'  高亮: {stats[\"highlights_added\"]}个')", "    print(f'  状态: {stats[\"status\"]}')", "    ", "    # 查看所有匹配位置", "    for match in stats['all_matches']:", "        print(f'  页面{match[\"page_num\"]}: {match[\"text_content\"]}')"]}, "position_extraction": {"python": ["# 提取所有匹配项的位置信息", "positions = []", "for stats in result['keywords_by_id'].values():", "    for match in stats['all_matches']:", "        positions.append({", "            'keyword': stats['text'],", "            'page': match['page_num'],", "            'bbox': match['bbox'],", "            'content': match['text_content']", "        })", "", "# 按页面分组", "from collections import defaultdict", "by_page = defaultdict(list)", "for pos in positions:", "    by_page[pos['page']].append(pos)"]}}}