#!/usr/bin/env python3
"""
详细匹配信息功能测试脚本

测试新增的详细匹配信息功能：
1. 所有匹配项的位置信息
2. 匹配文本内容和上下文
3. 成功/失败的高亮记录
4. 完整的处理结果统计
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置详细日志
logging.basicConfig(level=logging.INFO)
logger = get_logger()


def create_detailed_test_config():
    """创建详细测试配置文件"""
    config = {
        "keywords": [
            {
                "id": "review_keyword",
                "text": "审核",
                "color": "red",
                "opacity": 0.8,
                "priority": 1
            },
            {
                "id": "submit_keyword",
                "text": "提交",
                "color": "blue",
                "opacity": 0.7,
                "priority": 1
            },
            {
                "id": "app_context",
                "text": "应用",
                "color": "green",
                "opacity": 0.6,
                "context_phrase": "苹果ipa应用",
                "priority": 1
            },
            {
                "id": "not_found_keyword",
                "text": "这个关键词不存在",
                "color": "purple",
                "opacity": 0.5,
                "priority": 2
            }
        ],
        "global_options": {
            "context_fallback_strategy": "fallback_to_normal",
            "output_suffix": "_detailed_test"
        }
    }
    
    config_file = project_root / "examples" / "detailed_test_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"详细测试配置文件已创建: {config_file}")
    return config_file


def test_detailed_match_info():
    """测试详细匹配信息功能"""
    print("=== 详细匹配信息功能测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 创建配置文件
        config_file = create_detailed_test_config()
        
        # 创建高亮器并加载配置
        highlighter = MultiKeywordHighlighter(str(test_file))
        highlighter.load_config_file(str(config_file))
        
        print("\n配置的关键词:")
        for i, config in enumerate(highlighter.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else ""
            print(f"  {i}. [{config.id}] '{config.text}' -> {config.color}{context_info}")
        
        # 执行处理
        output_file = project_root / f"detailed_test_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file}")
        
        result = highlighter.process(str(output_file))
        
        if result['success']:
            print(f"✅ 处理成功！")
            
            # 显示匹配信息汇总
            matches_summary = result.get('matches_summary', {})
            print(f"\n📊 匹配信息汇总:")
            print(f"  总匹配数: {matches_summary.get('total_matches_found', 0)}")
            print(f"  总高亮数: {matches_summary.get('total_highlights_applied', 0)}")
            
            # 显示每个关键词的匹配情况
            print(f"\n🔍 各关键词匹配情况:")
            matches_by_keyword = matches_summary.get('matches_by_keyword', {})
            for keyword_id, match_count in matches_by_keyword.items():
                print(f"  [{keyword_id}]: {match_count} 个匹配")
            
            # 显示详细的关键词处理结果
            print(f"\n📋 详细处理结果:")
            for keyword_id, stats in result['keywords_by_id'].items():
                print(f"\n  🏷️ [{keyword_id}] '{stats['text']}':")
                print(f"     状态: {stats['status']}")
                print(f"     匹配数: {stats['matches_found']}")
                print(f"     高亮数: {stats['highlights_added']}")
                
                # 显示所有匹配项的详细信息
                all_matches = stats.get('all_matches', [])
                if all_matches:
                    print(f"     📍 所有匹配项:")
                    for i, match in enumerate(all_matches, 1):
                        print(f"       {i}. 页面 {match['page_num']}: '{match['text_content']}'")
                        print(f"          位置: {match['bbox']}")
                        if match.get('context_text'):
                            context = match['context_text'][:100] + "..." if len(match['context_text']) > 100 else match['context_text']
                            print(f"          上下文: {context}")
                
                # 显示成功应用的高亮
                highlights_applied = stats.get('highlights_applied', [])
                if highlights_applied:
                    print(f"     ✅ 成功应用的高亮:")
                    for highlight in highlights_applied:
                        print(f"       - 页面 {highlight['page_num']}: '{highlight['text_content']}'")
                
                # 显示失败的高亮
                highlights_failed = stats.get('highlights_failed', [])
                if highlights_failed:
                    print(f"     ❌ 失败的高亮:")
                    for failed in highlights_failed:
                        print(f"       - 页面 {failed['page_num']}: '{failed['text_content']}' (错误: {failed['error']})")
                
                # 显示配置信息
                config_info = []
                if stats.get('pages'):
                    config_info.append(f"页面: {stats['pages']}")
                if stats.get('context_phrase'):
                    config_info.append(f"上下文: {stats['context_phrase']}")
                if stats.get('priority'):
                    config_info.append(f"优先级: {stats['priority']}")
                
                if config_info:
                    print(f"     ⚙️ 配置: {', '.join(config_info)}")
            
            # 显示统计摘要
            summary = result['summary']
            print(f"\n📈 统计摘要:")
            print(f"  总关键词: {summary['total_keywords']}")
            print(f"  成功: {summary['successful_keywords']}")
            print(f"  失败: {summary['failed_keywords']}")
            print(f"  跳过: {summary['skipped_keywords']}")
            
        else:
            print(f"❌ 处理失败: {result['error']}")
            
            # 即使失败也显示详细信息
            if 'keywords_by_id' in result:
                print(f"\n详细失败信息:")
                for keyword_id, stats in result['keywords_by_id'].items():
                    print(f"  [{keyword_id}] '{stats['text']}': {stats['status']}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 详细匹配信息功能测试完成！")
    return True


def test_api_detailed_info():
    """测试编程API的详细信息功能"""
    print(f"\n=== 编程API详细信息测试 ===")
    
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        return
    
    test_file = test_files[0]
    
    try:
        highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 添加不同类型的关键词
        id1 = highlighter.add_keyword("审核", color="red", id="api_review")
        id2 = highlighter.add_keyword("应用", color="green", id="api_app", context_phrase="苹果ipa应用")
        id3 = highlighter.add_keyword("不存在", color="blue", id="api_not_found")
        
        print(f"添加的关键词ID: {id1}, {id2}, {id3}")
        
        result = highlighter.process()
        
        if result['success']:
            print(f"✅ API测试成功")
            
            # 验证返回结果的完整性
            required_fields = ['keywords_by_id', 'matches_summary', 'summary']
            missing_fields = [field for field in required_fields if field not in result]
            
            if not missing_fields:
                print("✅ 返回结果包含所有必需字段")
            else:
                print(f"⚠️ 缺少字段: {missing_fields}")
            
            # 验证每个关键词的详细信息
            for keyword_id in [id1, id2, id3]:
                if keyword_id in result['keywords_by_id']:
                    stats = result['keywords_by_id'][keyword_id]
                    required_stats = ['id', 'text', 'matches_found', 'highlights_added', 'all_matches', 'status']
                    missing_stats = [field for field in required_stats if field not in stats]
                    
                    if not missing_stats:
                        print(f"✅ [{keyword_id}] 包含完整的统计信息")
                    else:
                        print(f"⚠️ [{keyword_id}] 缺少统计字段: {missing_stats}")
                else:
                    print(f"❌ [{keyword_id}] 未在结果中找到")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")


if __name__ == "__main__":
    test_detailed_match_info()
    test_api_detailed_info()
    
    print("\n" + "="*70)
    print("详细匹配信息功能特点:")
    print("1. ✅ 记录所有匹配项的位置和内容")
    print("2. ✅ 提供匹配文本的上下文信息")
    print("3. ✅ 区分成功和失败的高亮操作")
    print("4. ✅ 包含完整的配置信息")
    print("5. ✅ 提供详细的统计摘要")
    print("6. ✅ 支持后续数据处理和分析")
    print("="*70)
