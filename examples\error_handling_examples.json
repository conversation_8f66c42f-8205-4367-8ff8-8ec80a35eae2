{"description": "错误处理和降级策略示例配置文件", "keywords": [{"comment": "正常配置 - 关键字在上下文中存在", "text": "应用", "color": "green", "context_phrase": "苹果ipa应用", "priority": 1}, {"comment": "错误配置1 - 关键字被数字分隔，系统会自动降级为普通搜索", "text": "审核", "color": "red", "context_phrase": "添加以供1审2核", "priority": 1}, {"comment": "错误配置2 - 关键字被特殊字符分隔，系统会自动降级", "text": "提交", "color": "blue", "context_phrase": "点击-提@交-按钮", "priority": 1}, {"comment": "错误配置3 - 关键字完全不存在于上下文中，系统会自动降级", "text": "删除", "color": "orange", "context_phrase": "保存文件操作", "priority": 1}, {"comment": "错误配置4 - 空的上下文短语，系统会忽略上下文配置", "text": "保存", "color": "yellow", "context_phrase": "", "priority": 1}, {"comment": "错误配置5 - null上下文短语，正常处理", "text": "修改", "color": "purple", "context_phrase": null, "priority": 1}, {"comment": "正常配置 - 没有上下文配置的普通关键字", "text": "普通关键词", "color": "gray", "priority": 2}], "global_options": {"output_suffix": "_error_handling_demo"}, "error_handling_notes": ["当context_phrase不包含指定的text时，系统会：", "1. 记录警告日志，不会抛出错误", "2. 自动清除无效的context_phrase配置", "3. 降级为普通关键字搜索模式", "4. 继续正常处理，不会中断程序", "5. 在日志中提供清晰的提示信息"], "supported_error_cases": ["关键字被数字分隔：'审核' 在 '添加以供1审2核' 中", "关键字被特殊字符分隔：'提交' 在 '点击-提@交-按钮' 中", "关键字完全不存在：'删除' 在 '保存文件操作' 中", "空的上下文短语：context_phrase 为 ''", "null上下文短语：context_phrase 为 null"]}