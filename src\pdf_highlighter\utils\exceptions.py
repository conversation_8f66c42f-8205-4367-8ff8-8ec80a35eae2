"""
自定义异常类定义

提供PDF处理过程中各种错误情况的专用异常类，支持中文错误信息。
"""


class PDFHighlighterError(Exception):
    """PDF高亮工具基础异常类
    
    所有自定义异常的基类，提供统一的错误处理接口。
    """
    
    def __init__(self, message: str, error_code: str = None, details: str = None):
        """初始化异常
        
        Args:
            message: 错误信息
            error_code: 错误代码
            details: 详细错误信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details
    
    def __str__(self):
        """返回格式化的错误信息"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def get_full_message(self):
        """获取包含详细信息的完整错误信息"""
        parts = [str(self)]
        if self.details:
            parts.append(f"详细信息: {self.details}")
        return "\n".join(parts)


class PDFError(PDFHighlighterError):
    """PDF文件相关错误
    
    包括文件打开、读取、保存等PDF操作相关的错误。
    """
    
    def __init__(self, message: str, file_path: str = None, **kwargs):
        super().__init__(message, error_code="PDF_ERROR", **kwargs)
        self.file_path = file_path


class SearchError(PDFHighlighterError):
    """搜索相关错误
    
    包括关键词搜索、正则表达式错误等搜索操作相关的错误。
    """
    
    def __init__(self, message: str, keyword: str = None, **kwargs):
        super().__init__(message, error_code="SEARCH_ERROR", **kwargs)
        self.keyword = keyword


class ValidationError(PDFHighlighterError):
    """参数验证错误
    
    包括输入参数格式错误、范围错误等验证相关的错误。
    """
    
    def __init__(self, message: str, parameter: str = None, value=None, **kwargs):
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)
        self.parameter = parameter
        self.value = value


class HighlightError(PDFHighlighterError):
    """高亮处理错误
    
    包括高亮注释添加、颜色设置等高亮操作相关的错误。
    """
    
    def __init__(self, message: str, page_num: int = None, **kwargs):
        super().__init__(message, error_code="HIGHLIGHT_ERROR", **kwargs)
        self.page_num = page_num


class ConfigurationError(PDFHighlighterError):
    """配置错误
    
    包括配置文件读取、设置错误等配置相关的错误。
    """
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)
        self.config_key = config_key


class PermissionError(PDFHighlighterError):
    """权限错误

    包括文件读写权限、目录访问权限等权限相关的错误。
    """

    def __init__(self, message: str, path: str = None, **kwargs):
        super().__init__(message, error_code="PERMISSION_ERROR", **kwargs)
        self.path = path


# 常用错误信息模板
ERROR_MESSAGES = {
    'pdf_not_found': "PDF文件不存在: {file_path}",
    'pdf_corrupted': "PDF文件已损坏或格式不正确: {file_path}",
    'pdf_encrypted': "PDF文件已加密，无法处理: {file_path}",
    'pdf_save_failed': "保存PDF文件失败: {file_path}",

    'keyword_empty': "搜索关键词不能为空",
    'keyword_too_long': "搜索关键词过长，最大长度为{max_length}字符",
    'regex_invalid': "无效的正则表达式: {pattern}",
    'no_matches_found': "未找到关键词 '{keyword}' 的匹配项",

    'pages_invalid_format': "页码格式无效: {pages}，请使用格式如 '1,3-5,7'",
    'pages_out_of_range': "页码超出范围: {page}，有效范围是 1-{total_pages}",
    'occurrence_invalid': "关键词序号无效: {occurrence}，必须是正整数",
    'occurrence_out_of_range': "关键词序号超出范围: {occurrence}，总共只有 {total_matches} 个匹配项",
    'color_invalid': "颜色格式无效: {color}",
    'opacity_invalid': "透明度值无效: {opacity}，必须在0-1范围内",

    'highlight_add_failed': "添加高亮注释失败，页码: {page_num}",
    'highlight_color_failed': "设置高亮颜色失败: {color}",

    'config_load_failed': "加载配置失败: {config_file}",
    'config_key_missing': "配置项缺失: {key}",

    'permission_read_denied': "没有读取权限: {path}",
    'permission_write_denied': "没有写入权限: {path}",
    'directory_create_failed': "创建目录失败: {path}",
}


def create_error(error_type: str, **kwargs) -> PDFHighlighterError:
    """创建标准化错误对象

    Args:
        error_type: 错误类型，对应ERROR_MESSAGES中的键
        **kwargs: 错误信息格式化参数

    Returns:
        PDFHighlighterError: 对应的异常对象
    """
    if error_type not in ERROR_MESSAGES:
        return PDFHighlighterError(f"未知错误类型: {error_type}")

    message = ERROR_MESSAGES[error_type].format(**kwargs)

    # 根据错误类型返回对应的异常类
    if error_type.startswith('pdf_'):
        return PDFError(message, file_path=kwargs.get('file_path'))
    elif error_type.startswith(('keyword_', 'regex_', 'no_matches_')):
        return SearchError(message, keyword=kwargs.get('keyword'))
    elif error_type.startswith(('pages_', 'occurrence_', 'color_', 'opacity_')):
        return ValidationError(message, parameter=error_type, value=kwargs.get(error_type.split('_')[0]))
    elif error_type.startswith('highlight_'):
        return HighlightError(message, page_num=kwargs.get('page_num'))
    elif error_type.startswith('config_'):
        return ConfigurationError(message, config_key=kwargs.get('key'))
    elif error_type.startswith(('permission_', 'directory_')):
        return PermissionError(message, path=kwargs.get('path'))
    else:
        return PDFHighlighterError(message)
