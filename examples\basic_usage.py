#!/usr/bin/env python3
"""
PDF高亮工具基本使用示例

演示如何使用pdf_highlighter进行基本的PDF关键词高亮操作。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import PDFHighlighter, get_logger


def basic_highlight_example():
    """基本高亮示例"""
    print("=== 基本高亮示例 ===")
    
    # 设置日志
    logger = get_logger()
    
    # 检查测试文件是否存在
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    try:
        # 使用上下文管理器打开PDF
        with PDFHighlighter(str(test_file)) as highlighter:
            print(f"PDF文件: {test_file}")
            print(f"总页数: {highlighter.get_page_count()}")
            
            # 搜索关键词
            keyword = "前端"
            print(f"\n搜索关键词: '{keyword}'")
            
            results = highlighter.search_keywords(keyword)
            
            if results:
                total_matches = sum(len(quads) for _, quads in results)
                print(f"找到 {total_matches} 个匹配项，分布在 {len(results)} 页中")
                
                # 显示匹配详情
                for page_num, quads in results:
                    print(f"  第{page_num}页: {len(quads)}个匹配项")
                
                # 添加黄色高亮
                highlight_count = highlighter.add_highlights(
                    results, 
                    color=(1, 1, 0),  # 黄色
                    opacity=0.5
                )
                
                # 保存到新文件
                output_file = project_root / "examples" / "basic_highlighted.pdf"
                highlighter.save_pdf(str(output_file))
                
                print(f"\n成功添加 {highlight_count} 个高亮注释")
                print(f"输出文件: {output_file}")
                
            else:
                print(f"未找到关键词 '{keyword}' 的匹配项")
                
    except Exception as e:
        logger.error(f"处理失败: {e}")


def multiple_keywords_example():
    """多关键词高亮示例"""
    print("\n=== 多关键词高亮示例 ===")
    
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    # 定义要搜索的关键词和对应颜色
    keywords_colors = [
        ("前端", (1, 1, 0)),      # 黄色
        ("工程师", (0, 1, 0)),    # 绿色
        ("开发", (0, 0, 1)),      # 蓝色
    ]
    
    try:
        with PDFHighlighter(str(test_file)) as highlighter:
            total_highlights = 0
            
            for keyword, color in keywords_colors:
                print(f"\n处理关键词: '{keyword}'")
                
                results = highlighter.search_keywords(keyword)
                
                if results:
                    matches = sum(len(quads) for _, quads in results)
                    print(f"  找到 {matches} 个匹配项")
                    
                    count = highlighter.add_highlights(results, color=color, opacity=0.6)
                    total_highlights += count
                    print(f"  添加 {count} 个高亮")
                else:
                    print(f"  未找到匹配项")
            
            if total_highlights > 0:
                output_file = project_root / "examples" / "multiple_keywords_highlighted.pdf"
                highlighter.save_pdf(str(output_file))
                print(f"\n总共添加 {total_highlights} 个高亮注释")
                print(f"输出文件: {output_file}")
            else:
                print("\n没有找到任何匹配项")
                
    except Exception as e:
        print(f"处理失败: {e}")


def page_range_example():
    """页码范围示例"""
    print("\n=== 页码范围示例 ===")
    
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    try:
        with PDFHighlighter(str(test_file)) as highlighter:
            keyword = "前端"
            pages = [1, 2]  # 只在第1-2页搜索
            
            print(f"在第{pages}页搜索关键词: '{keyword}'")
            
            results = highlighter.search_keywords(keyword, pages=pages)
            
            if results:
                matches = sum(len(quads) for _, quads in results)
                print(f"找到 {matches} 个匹配项")
                
                # 只高亮第一个匹配项
                count = highlighter.add_highlights(results, occurrence=1, color=(1, 0, 0))
                
                output_file = project_root / "examples" / "page_range_highlighted.pdf"
                highlighter.save_pdf(str(output_file))
                
                print(f"只高亮第一个匹配项: {count} 个高亮")
                print(f"输出文件: {output_file}")
            else:
                print("未找到匹配项")
                
    except Exception as e:
        print(f"处理失败: {e}")


def regex_search_example():
    """正则表达式搜索示例"""
    print("\n=== 正则表达式搜索示例 ===")
    
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    try:
        with PDFHighlighter(str(test_file)) as highlighter:
            # 使用正则表达式搜索
            pattern = r"前端|工程师|开发"
            print(f"正则表达式搜索: '{pattern}'")
            
            results = highlighter.search_keywords(
                pattern, 
                regex=True, 
                case_sensitive=False
            )
            
            if results:
                matches = sum(len(quads) for _, quads in results)
                print(f"找到 {matches} 个匹配项")
                
                count = highlighter.add_highlights(results, color=(1, 0, 1), opacity=0.7)
                
                output_file = project_root / "examples" / "regex_highlighted.pdf"
                highlighter.save_pdf(str(output_file))
                
                print(f"添加 {count} 个高亮注释")
                print(f"输出文件: {output_file}")
            else:
                print("未找到匹配项")
                
    except Exception as e:
        print(f"处理失败: {e}")


def main():
    """主函数"""
    print("PDF高亮工具基本使用示例")
    print("=" * 50)
    
    # 创建输出目录
    examples_dir = project_root / "examples"
    examples_dir.mkdir(exist_ok=True)
    
    # 运行各种示例
    basic_highlight_example()
    multiple_keywords_example()
    page_range_example()
    regex_search_example()
    
    print("\n" + "=" * 50)
    print("所有示例执行完成！")
    print(f"输出文件保存在: {examples_dir}")


if __name__ == "__main__":
    main()
