#!/usr/bin/env python3
"""
验证返回结果结构
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from pdf_highlighter import MultiKeywordHighlighter
    
    # 找到PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到PDF文件")
        exit(1)
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 创建高亮器
    highlighter = MultiKeywordHighlighter(str(test_file))
    
    # 添加测试关键词
    id1 = highlighter.add_keyword("审核", color="red", id="test_review")
    id2 = highlighter.add_keyword("不存在的关键词", color="blue", id="test_not_found")
    
    print(f"添加的关键词ID: {id1}, {id2}")
    
    # 执行处理
    result = highlighter.process()
    
    print(f"\n=== 返回结果结构验证 ===")
    print(f"success: {result.get('success')}")
    print(f"total_highlights: {result.get('total_highlights')}")
    
    # 检查 keywords_by_id 结构
    if 'keywords_by_id' in result:
        print(f"\nkeywords_by_id 包含 {len(result['keywords_by_id'])} 个关键词:")
        
        for keyword_id, stats in result['keywords_by_id'].items():
            print(f"\n  [{keyword_id}]:")
            print(f"    id: {stats.get('id')}")
            print(f"    text: {stats.get('text')}")
            print(f"    matches_found: {stats.get('matches_found')}")
            print(f"    highlights_added: {stats.get('highlights_added')}")
            print(f"    status: {stats.get('status')}")
            
            # 检查是否包含不应该有的复杂字段
            complex_fields = ['all_matches', 'highlights_applied', 'highlights_failed', 'bbox', 'context_text']
            found_complex = [field for field in complex_fields if field in stats]
            
            if found_complex:
                print(f"    ⚠️ 包含复杂字段: {found_complex}")
            else:
                print(f"    ✅ 结构简洁")
    
    # 检查 summary 结构
    if 'summary' in result:
        summary = result['summary']
        print(f"\nsummary:")
        print(f"  total_keywords: {summary.get('total_keywords')}")
        print(f"  successful_keywords: {summary.get('successful_keywords')}")
        print(f"  failed_keywords: {summary.get('failed_keywords')}")
    
    # 输出完整结果（用于调试）
    print(f"\n=== 完整结果（JSON格式）===")
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
