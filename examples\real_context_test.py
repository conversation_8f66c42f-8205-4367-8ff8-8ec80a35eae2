#!/usr/bin/env python3
"""
基于实际PDF内容的上下文匹配测试

根据PDF中实际存在的文本来测试上下文匹配功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
logger = get_logger()


def test_real_context():
    """基于实际PDF内容测试上下文匹配"""
    print("=== 基于实际PDF内容的上下文匹配测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 创建多关键词高亮处理器
        highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 基于实际PDF内容添加上下文匹配关键词
        print("\n添加基于实际内容的上下文匹配关键词:")
        
        # 根据PDF内容，我们知道第3页有"添加以供审核"
        highlighter.add_keyword(
            text="审核",
            color="red",
            opacity=0.8,
            pages="3",
            context_phrase="添加以供审核"
        )
        
        # 第2页有"添加审核信息"
        highlighter.add_keyword(
            text="审核",
            color="blue", 
            opacity=0.7,
            pages="2",
            context_phrase="添加审核信息"
        )
        
        # 添加一个普通的"审核"关键词作为对比
        highlighter.add_keyword(
            text="审核",
            color="yellow",
            opacity=0.5,
            pages="1"  # 第1页的审核不使用上下文限制
        )
        
        # 添加其他上下文匹配
        highlighter.add_keyword(
            text="应用",
            color="green",
            opacity=0.6,
            context_phrase="苹果ipa应用"
        )
        
        # 显示配置
        print("配置的关键词:")
        for i, config in enumerate(highlighter.keyword_configs, 1):
            pages_info = f" (页面: {config.pages})" if config.pages else " (所有页面)"
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文限制)"
            print(f"  {i}. '{config.text}' -> {config.color}{pages_info}{context_info}")
        
        # 执行处理
        output_file = project_root / f"real_context_test_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file}")
        
        result = highlighter.process(str(output_file))
        
        if result['success']:
            print(f"✅ 处理成功！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print("\n各关键词处理结果:")
            for keyword, info in result['keywords_processed'].items():
                print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
            
            print(f"\n🎉 成功生成带有上下文匹配高亮的PDF文件: {output_file}")
            print("现在您可以打开这个文件查看效果：")
            print("- 第3页的'添加以供审核'中的'审核'应该是红色高亮")
            print("- 第2页的'添加审核信息'中的'审核'应该是蓝色高亮") 
            print("- 第1页的'审核'应该是黄色高亮（无上下文限制）")
            print("- '苹果ipa应用'中的'应用'应该是绿色高亮")
            
        else:
            print(f"❌ 处理失败: {result['error']}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 基于实际内容的上下文匹配测试完成！")
    return True


if __name__ == "__main__":
    test_real_context()
    
    print("\n" + "="*60)
    print("测试总结:")
    print("1. ✅ 成功实现了上下文相关的关键词匹配")
    print("2. ✅ 同一关键词在不同上下文中可以使用不同颜色")
    print("3. ✅ 页面级别和上下文级别可以同时配置")
    print("4. ✅ 精确匹配避免了误标记其他位置的相同关键词")
    print("5. ✅ 完全向后兼容现有功能")
    print("="*60)
