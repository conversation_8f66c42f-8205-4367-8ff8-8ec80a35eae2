# 多关键词高亮功能使用指南

PDF高亮工具现在支持在一次执行中对多个关键词使用不同颜色进行高亮，提供了三种使用方式：

## 🎯 功能特性

- ✅ **多关键词支持**：一次处理多个关键词，每个关键词可设置不同颜色
- ✅ **配置文件模式**：使用JSON配置文件进行复杂配置
- ✅ **命令行模式**：直接在命令行指定多个关键词和颜色
- ✅ **编程API**：完整的Python API支持
- ✅ **精细控制**：每个关键词可独立设置颜色、透明度、搜索选项
- ✅ **批量处理**：支持批量处理多个PDF文件

## 📖 使用方式

### 1. 配置文件模式（推荐）

#### 创建配置文件
```bash
# 创建示例配置文件
pdf-highlight --create-config my_config.json
```

#### 配置文件格式
```json
{
  "keywords": [
    {
      "text": "前端开发",
      "color": "yellow",
      "opacity": 0.6,
      "case_sensitive": false,
      "regex": false
    },
    {
      "text": "后端开发", 
      "color": "blue",
      "opacity": 0.7,
      "case_sensitive": false,
      "regex": false
    },
    {
      "text": "Python|Java|JavaScript",
      "color": "orange",
      "opacity": 0.8,
      "case_sensitive": false,
      "regex": true
    },
    {
      "text": "API",
      "color": "#FF6B6B",
      "opacity": 0.6,
      "case_sensitive": true,
      "regex": false,
      "whole_word": true,
      "occurrence": 5
    }
  ],
  "global_options": {
    "pages": null,
    "occurrence": null,
    "whole_word": false,
    "output_suffix": "_multi_highlighted"
  }
}
```

#### 使用配置文件
```bash
# 预览模式
pdf-highlight input.pdf --config my_config.json --dry-run

# 实际执行
pdf-highlight input.pdf --config my_config.json

# 指定输出文件
pdf-highlight input.pdf --config my_config.json -o output.pdf
```

### 2. 命令行模式

```bash
# 基本用法：多个关键词使用默认颜色
pdf-highlight input.pdf --keywords "前端" "后端" "工程师"

# 指定对应颜色
pdf-highlight input.pdf --keywords "前端" "后端" "工程师" --colors yellow blue green

# 预览模式
pdf-highlight input.pdf --keywords "前端" "后端" --colors yellow blue --dry-run

# 结合其他选项
pdf-highlight input.pdf --keywords "前端" "后端" --colors yellow blue -o output.pdf --regex
```

### 3. Python编程API

```python
from pdf_highlighter import MultiKeywordHighlighter, KeywordConfig

# 方式1：使用add_keyword方法
highlighter = MultiKeywordHighlighter('input.pdf')

highlighter.add_keyword('前端', color='yellow', opacity=0.6)
highlighter.add_keyword('后端', color='blue', opacity=0.7)
highlighter.add_keyword('工程师', color='green', opacity=0.5)

result = highlighter.process('output.pdf')

# 方式2：使用配置文件
highlighter = MultiKeywordHighlighter('input.pdf')
highlighter.load_config_file('config.json')
result = highlighter.process()

# 方式3：使用KeywordConfig类
configs = [
    KeywordConfig(text='前端', color='yellow', opacity=0.6),
    KeywordConfig(text='后端', color='blue', opacity=0.7, regex=True),
    KeywordConfig(text='API', color='red', case_sensitive=True, whole_word=True)
]

highlighter = MultiKeywordHighlighter('input.pdf')
highlighter.keyword_configs = configs
result = highlighter.process()
```

## 🎨 颜色格式支持

支持多种颜色格式：

```json
{
  "keywords": [
    {"text": "关键词1", "color": "yellow"},           // 颜色名称
    {"text": "关键词2", "color": "#FF6B6B"},         // 十六进制
    {"text": "关键词3", "color": "255,0,0"},         // RGB字符串
    {"text": "关键词4", "color": [1, 0.5, 0]}        // RGB数组
  ]
}
```

## ⚙️ 配置选项说明

### 关键词级别选项
- `text`: 关键词文本（必需）
- `color`: 高亮颜色（默认：yellow）
- `opacity`: 透明度 0-1（默认：0.5）
- `case_sensitive`: 区分大小写（默认：false）
- `regex`: 正则表达式模式（默认：false）
- `whole_word`: 完整单词匹配（默认：false）
- `occurrence`: 只高亮前N个匹配项（可选）

### 全局选项
- `pages`: 页码范围（如："1,3-5,7"）
- `occurrence`: 全局匹配项限制
- `whole_word`: 全局完整单词匹配
- `output_suffix`: 输出文件后缀

## 📝 实际应用示例

### 学术论文标注
```json
{
  "keywords": [
    {"text": "深度学习", "color": "blue", "opacity": 0.6},
    {"text": "神经网络", "color": "green", "opacity": 0.6},
    {"text": "机器学习", "color": "orange", "opacity": 0.6},
    {"text": "算法|模型", "color": "purple", "opacity": 0.7, "regex": true}
  ]
}
```

### 技术文档高亮
```json
{
  "keywords": [
    {"text": "function|class|method", "color": "blue", "regex": true},
    {"text": "API", "color": "red", "case_sensitive": true, "whole_word": true},
    {"text": "TODO|FIXME|BUG", "color": "yellow", "regex": true},
    {"text": "import|export", "color": "green", "regex": true}
  ]
}
```

### 合同文档审查
```json
{
  "keywords": [
    {"text": "甲方|乙方", "color": "red", "regex": true},
    {"text": "违约责任", "color": "yellow", "opacity": 0.8},
    {"text": "终止|解除", "color": "orange", "regex": true},
    {"text": "保密|机密", "color": "purple", "regex": true}
  ]
}
```

## 🚀 批量处理

```python
from pdf_highlighter import MultiKeywordHighlighter

# 批量处理多个文件
file_list = ['doc1.pdf', 'doc2.pdf', 'doc3.pdf']
config_file = 'multi_config.json'

for i, pdf_file in enumerate(file_list):
    highlighter = MultiKeywordHighlighter(pdf_file)
    highlighter.load_config_file(config_file)
    
    output_file = f'highlighted_{i+1}.pdf'
    result = highlighter.process(output_file)
    
    if result['success']:
        print(f"✓ {pdf_file}: {result['total_highlights']} 个高亮")
    else:
        print(f"✗ {pdf_file}: {result['error']}")
```

## 💡 最佳实践

1. **配置文件优先**：复杂配置建议使用配置文件模式
2. **颜色搭配**：选择对比度高的颜色组合，便于区分
3. **透明度设置**：建议0.5-0.8之间，既突出又不影响阅读
4. **正则表达式**：善用正则表达式处理相关词汇
5. **批量处理**：大量文件建议使用Python API进行批量处理

## 🔧 故障排除

### 常见问题

1. **配置文件格式错误**
   ```
   错误：JSON格式不正确
   解决：使用--create-config创建标准配置文件模板
   ```

2. **颜色格式不支持**
   ```
   错误：无效的颜色格式
   解决：使用支持的颜色格式（名称、十六进制、RGB）
   ```

3. **关键词数量与颜色数量不匹配**
   ```
   错误：颜色数量与关键词数量不匹配
   解决：确保--colors参数数量与--keywords参数数量一致
   ```

4. **正则表达式错误**
   ```
   错误：无效的正则表达式
   解决：检查正则表达式语法，使用在线工具验证
   ```

## 📊 性能优化

- **大文件处理**：使用页码范围限制处理范围
- **复杂正则**：避免过于复杂的正则表达式
- **批量处理**：使用多线程处理大量文件
- **内存管理**：处理完成后及时释放资源

---

更多详细信息请参考主项目文档或使用 `pdf-highlight --help` 查看帮助信息。
