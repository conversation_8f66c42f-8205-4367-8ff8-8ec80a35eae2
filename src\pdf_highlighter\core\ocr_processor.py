"""
OCR图像处理模块

实现PDF图片提取、OCR文字识别、坐标转换等核心功能。
支持中文文字识别和图片预处理优化。
"""

import io
import re
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Tuple, Optional, Dict, Any, Union
from dataclasses import dataclass
from pathlib import Path
import pymupdf

try:
    import pytesseract
    from PIL import Image, ImageEnhance, ImageFilter
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

from pdf_highlighter.config.settings import get_config
from pdf_highlighter.utils.logger import get_logger
from pdf_highlighter.utils.exceptions import PDFHighlighterError


@dataclass
class OCRResult:
    """OCR识别结果"""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x0, y0, x1, y1) 在图片中的坐标
    page_bbox: Optional[Tuple[float, float, float, float]] = None  # 转换后的PDF页面坐标


@dataclass
class ImageInfo:
    """图片信息"""
    image_index: int
    bbox: Tuple[float, float, float, float]  # 图片在PDF页面中的位置
    width: int
    height: int
    image_data: bytes


class OCRDependencyError(PDFHighlighterError):
    """OCR依赖缺失错误"""
    pass


class OCREngine:
    """OCR识别引擎"""
    
    def __init__(self):
        """初始化OCR引擎"""
        if not OCR_AVAILABLE:
            raise OCRDependencyError(
                "OCR功能需要安装pytesseract和Pillow库。"
                "请运行: pip install pdf-highlighter[ocr]"
            )
        
        self.logger = get_logger()
        self._check_tesseract_installation()
    
    def _check_tesseract_installation(self) -> None:
        """检查Tesseract是否正确安装"""
        try:
            pytesseract.get_tesseract_version()
        except Exception as e:
            raise OCRDependencyError(
                f"Tesseract OCR引擎未正确安装: {e}\n"
                "请参考文档安装Tesseract OCR引擎。"
            )
    
    def recognize_text(self, image,
                      language: str = None,
                      confidence_threshold: int = None) -> List[OCRResult]:
        """识别图片中的文字
        
        Args:
            image: PIL图片对象
            language: OCR语言设置
            confidence_threshold: 置信度阈值
            
        Returns:
            List[OCRResult]: OCR识别结果列表
        """
        if language is None:
            language = get_config('ocr.language', 'chi_sim+eng')
        if confidence_threshold is None:
            confidence_threshold = get_config('ocr.confidence_threshold', 60)
        
        try:
            # 使用pytesseract进行OCR识别，获取详细数据
            data = pytesseract.image_to_data(
                image, 
                lang=language,
                output_type=pytesseract.Output.DICT,
                config='--psm 6'  # 假设单一文本块
            )
            
            results = []
            n_boxes = len(data['text'])
            
            for i in range(n_boxes):
                text = data['text'][i].strip()
                confidence = float(data['conf'][i])
                
                # 过滤低置信度和空文本
                if confidence >= confidence_threshold and text:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    bbox = (x, y, x + w, y + h)
                    
                    results.append(OCRResult(
                        text=text,
                        confidence=confidence,
                        bbox=bbox
                    ))
            
            self.logger.debug(f"OCR识别完成，找到{len(results)}个文本块")
            return results
            
        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            return []


class ImageExtractor:
    """PDF图片提取器"""
    
    def __init__(self):
        """初始化图片提取器"""
        self.logger = get_logger()
    
    def extract_images_from_page(self, page: pymupdf.Page) -> List[ImageInfo]:
        """从PDF页面提取图片
        
        Args:
            page: PyMuPDF页面对象
            
        Returns:
            List[ImageInfo]: 图片信息列表
        """
        images = []
        
        try:
            # 获取页面中的图片列表
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                # 获取图片引用
                xref = img[0]
                
                # 提取图片数据
                base_image = page.parent.extract_image(xref)
                image_bytes = base_image["image"]
                
                # 获取图片在页面中的位置
                image_rects = page.get_image_rects(img)
                
                if image_rects:
                    # 使用第一个矩形作为图片位置
                    rect = image_rects[0]
                    bbox = (rect.x0, rect.y0, rect.x1, rect.y1)
                    
                    # 获取图片尺寸
                    try:
                        from PIL import Image as PILImage
                        pil_image = PILImage.open(io.BytesIO(image_bytes))
                        width, height = pil_image.size
                        
                        images.append(ImageInfo(
                            image_index=img_index,
                            bbox=bbox,
                            width=width,
                            height=height,
                            image_data=image_bytes
                        ))
                        
                    except Exception as e:
                        self.logger.warning(f"无法处理图片 {img_index}: {e}")
                        continue
            
            self.logger.debug(f"从页面提取了{len(images)}张图片")
            return images
            
        except Exception as e:
            self.logger.error(f"图片提取失败: {e}")
            return []


class ImagePreprocessor:
    """图片预处理器"""
    
    def __init__(self):
        """初始化图片预处理器"""
        self.logger = get_logger()
    
    def preprocess_image(self, image):
        """预处理图片以提高OCR精度
        
        Args:
            image: 原始图片
            
        Returns:
            Image.Image: 预处理后的图片
        """
        try:
            from PIL import Image as PILImage, ImageEnhance, ImageFilter

            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # 获取预处理配置
            enable_denoising = get_config('ocr.image_preprocessing.enable_denoising', True)
            enable_binarization = get_config('ocr.image_preprocessing.enable_binarization', True)
            scale_factor = get_config('ocr.image_preprocessing.scale_factor', 2.0)

            # 放大图片提高OCR精度
            if scale_factor > 1.0:
                new_size = (int(image.width * scale_factor), int(image.height * scale_factor))
                image = image.resize(new_size, PILImage.Resampling.LANCZOS)

            # 去噪处理
            if enable_denoising:
                image = image.filter(ImageFilter.MedianFilter(size=3))

            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)

            # 二值化处理
            if enable_binarization:
                image = image.convert('L')  # 转为灰度
                # 使用阈值进行二值化
                threshold = 128
                image = image.point(lambda x: 255 if x > threshold else 0, mode='1')
                image = image.convert('RGB')  # 转回RGB供OCR使用

            return image
            
        except Exception as e:
            self.logger.warning(f"图片预处理失败，使用原图: {e}")
            return image


class CoordinateTransformer:
    """坐标转换器"""
    
    def __init__(self):
        """初始化坐标转换器"""
        self.logger = get_logger()
    
    def transform_coordinates(self, ocr_result: OCRResult, 
                            image_info: ImageInfo,
                            scale_factor: float = 1.0) -> OCRResult:
        """将图片坐标转换为PDF页面坐标
        
        Args:
            ocr_result: OCR识别结果
            image_info: 图片信息
            scale_factor: 图片缩放因子
            
        Returns:
            OCRResult: 包含PDF页面坐标的OCR结果
        """
        try:
            # 获取OCR结果在图片中的坐标
            img_x0, img_y0, img_x1, img_y1 = ocr_result.bbox
            
            # 考虑预处理时的缩放
            if scale_factor > 1.0:
                img_x0 /= scale_factor
                img_y0 /= scale_factor
                img_x1 /= scale_factor
                img_y1 /= scale_factor
            
            # 获取图片在PDF页面中的位置
            pdf_x0, pdf_y0, pdf_x1, pdf_y1 = image_info.bbox
            
            # 计算图片的缩放比例
            img_width = image_info.width
            img_height = image_info.height
            pdf_width = pdf_x1 - pdf_x0
            pdf_height = pdf_y1 - pdf_y0
            
            x_scale = pdf_width / img_width
            y_scale = pdf_height / img_height
            
            # 转换坐标到PDF页面坐标系
            page_x0 = pdf_x0 + img_x0 * x_scale
            page_y0 = pdf_y0 + img_y0 * y_scale
            page_x1 = pdf_x0 + img_x1 * x_scale
            page_y1 = pdf_y0 + img_y1 * y_scale
            
            # 创建新的OCR结果
            result = OCRResult(
                text=ocr_result.text,
                confidence=ocr_result.confidence,
                bbox=ocr_result.bbox,
                page_bbox=(page_x0, page_y0, page_x1, page_y1)
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"坐标转换失败: {e}")
            return ocr_result


class OCRProcessor:
    """OCR处理器主类"""

    def __init__(self):
        """初始化OCR处理器"""
        self.logger = get_logger()
        self.ocr_engine = OCREngine()
        self.image_extractor = ImageExtractor()
        self.image_preprocessor = ImagePreprocessor()
        self.coordinate_transformer = CoordinateTransformer()

        # 缓存设置
        self.cache_enabled = get_config('ocr.cache_enabled', True)
        self.ocr_cache: Dict[str, List[OCRResult]] = {}
        self.cache_lock = threading.Lock()

    def search_text_in_images(self, page: pymupdf.Page,
                             keyword: str,
                             case_sensitive: bool = False,
                             regex: bool = False) -> List[Tuple[float, float, float, float]]:
        """在页面图片中搜索文字

        Args:
            page: PyMuPDF页面对象
            keyword: 搜索关键词
            case_sensitive: 是否区分大小写
            regex: 是否使用正则表达式

        Returns:
            List[Tuple]: 匹配文字的PDF页面坐标列表
        """
        if not OCR_AVAILABLE:
            self.logger.warning("OCR功能不可用，跳过图片文字搜索")
            return []

        try:
            # 提取页面图片
            images = self.image_extractor.extract_images_from_page(page)
            if not images:
                return []

            matches = []

            # 是否启用并行处理
            parallel_processing = get_config('ocr.parallel_processing', True)
            max_workers = get_config('ocr.max_workers', 4)

            if parallel_processing and len(images) > 1:
                # 并行处理多张图片
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_image = {
                        executor.submit(self._process_single_image, img, keyword, case_sensitive, regex): img
                        for img in images
                    }

                    for future in as_completed(future_to_image):
                        image_matches = future.result()
                        matches.extend(image_matches)
            else:
                # 串行处理
                for image_info in images:
                    image_matches = self._process_single_image(image_info, keyword, case_sensitive, regex)
                    matches.extend(image_matches)

            self.logger.info(f"在{len(images)}张图片中找到{len(matches)}个匹配项")
            return matches

        except Exception as e:
            self.logger.error(f"图片文字搜索失败: {e}")
            return []

    def _process_single_image(self, image_info: ImageInfo,
                             keyword: str,
                             case_sensitive: bool,
                             regex: bool) -> List[Tuple[float, float, float, float]]:
        """处理单张图片的OCR和搜索

        Args:
            image_info: 图片信息
            keyword: 搜索关键词
            case_sensitive: 是否区分大小写
            regex: 是否使用正则表达式

        Returns:
            List[Tuple]: 匹配文字的PDF页面坐标列表
        """
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(image_info.image_data)

            # 尝试从缓存获取OCR结果
            ocr_results = self._get_cached_ocr_results(cache_key)

            if ocr_results is None:
                # 执行OCR识别
                from PIL import Image as PILImage
                pil_image = PILImage.open(io.BytesIO(image_info.image_data))

                # 图片预处理
                processed_image = self.image_preprocessor.preprocess_image(pil_image)

                # OCR识别
                ocr_results = self.ocr_engine.recognize_text(processed_image)

                # 坐标转换
                scale_factor = get_config('ocr.image_preprocessing.scale_factor', 2.0)
                for i, result in enumerate(ocr_results):
                    ocr_results[i] = self.coordinate_transformer.transform_coordinates(
                        result, image_info, scale_factor
                    )

                # 缓存结果
                self._cache_ocr_results(cache_key, ocr_results)

            # 在OCR结果中搜索关键词
            matches = []
            for result in ocr_results:
                if self._text_matches(result.text, keyword, case_sensitive, regex):
                    if result.page_bbox:
                        matches.append(result.page_bbox)

            return matches

        except Exception as e:
            self.logger.error(f"处理图片失败: {e}")
            return []

    def _text_matches(self, text: str, keyword: str,
                     case_sensitive: bool, regex: bool) -> bool:
        """检查文本是否匹配关键词

        Args:
            text: 要检查的文本
            keyword: 关键词
            case_sensitive: 是否区分大小写
            regex: 是否使用正则表达式

        Returns:
            bool: 是否匹配
        """
        try:
            if regex:
                flags = 0 if case_sensitive else re.IGNORECASE
                pattern = re.compile(keyword, flags)
                return bool(pattern.search(text))
            else:
                if not case_sensitive:
                    text = text.lower()
                    keyword = keyword.lower()
                return keyword in text
        except Exception:
            return False

    def _generate_cache_key(self, image_data: bytes) -> str:
        """生成图片数据的缓存键"""
        import hashlib
        return hashlib.md5(image_data).hexdigest()

    def _get_cached_ocr_results(self, cache_key: str) -> Optional[List[OCRResult]]:
        """从缓存获取OCR结果"""
        if not self.cache_enabled:
            return None

        with self.cache_lock:
            return self.ocr_cache.get(cache_key)

    def _cache_ocr_results(self, cache_key: str, results: List[OCRResult]) -> None:
        """缓存OCR结果"""
        if not self.cache_enabled:
            return

        with self.cache_lock:
            # 检查缓存大小限制
            max_cache_size = get_config('ocr.max_cache_size', 100)
            if len(self.ocr_cache) >= max_cache_size:
                # 删除最旧的缓存项（简单的FIFO策略）
                oldest_key = next(iter(self.ocr_cache))
                del self.ocr_cache[oldest_key]

            self.ocr_cache[cache_key] = results

    def clear_cache(self) -> None:
        """清空OCR缓存"""
        with self.cache_lock:
            self.ocr_cache.clear()
            self.logger.info("OCR缓存已清空")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            return {
                'cache_enabled': self.cache_enabled,
                'cache_size': len(self.ocr_cache),
                'max_cache_size': get_config('ocr.max_cache_size', 100)
            }
