#!/usr/bin/env python3
"""
重叠处理功能测试脚本

测试优先级机制和冲突检测功能，验证：
1. 长关键词默认优先于短关键词
2. 用户配置的优先级覆盖默认优先级
3. 重叠区域的正确处理
4. 统计信息的准确性
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
logger = get_logger()


def test_overlap_handling():
    """测试重叠处理功能"""
    print("=== 重叠处理功能测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 测试1：编程API方式测试重叠处理
        print("\n=== 测试1：编程API方式 ===")
        highlighter1 = MultiKeywordHighlighter(str(test_file))
        
        # 添加会重叠的关键词
        print("添加重叠关键词配置:")
        
        # 在"添加以供审核"中，"以供审核"和"审核"会重叠
        highlighter1.add_keyword("以供审核", color="red", priority=1, context_phrase="添加以供审核")
        highlighter1.add_keyword("审核", color="blue", priority=2, context_phrase="添加以供审核")
        
        # 在"苹果ipa应用"中，"苹果ipa应用"、"应用"、"ipa"会重叠
        highlighter1.add_keyword("苹果ipa应用", color="green", priority=1)
        highlighter1.add_keyword("应用", color="orange", priority=2)
        highlighter1.add_keyword("ipa", color="purple", priority=3)
        
        # 显示配置
        print("配置的关键词:")
        for i, config in enumerate(highlighter1.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else ""
            print(f"  {i}. '{config.text}' -> {config.color} (优先级: {config.priority}){context_info}")
        
        # 执行处理
        output_file1 = project_root / f"overlap_test_api_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file1}")
        
        result1 = highlighter1.process(str(output_file1))
        
        if result1['success']:
            print(f"✅ 处理成功！")
            print(f"总共添加 {result1['total_highlights']} 个高亮")
            print("\n各关键词处理结果:")
            for keyword, info in result1['keywords_processed'].items():
                print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
        else:
            print(f"❌ 处理失败: {result1['error']}")
        
        # 测试2：配置文件方式测试
        print(f"\n=== 测试2：配置文件方式 ===")
        config_file = project_root / "examples" / "overlap_handling_config.json"
        
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        highlighter2.load_config_file(str(config_file))
        
        print("配置文件中的关键词:")
        for i, config in enumerate(highlighter2.keyword_configs, 1):
            pages_info = f" (页面: {config.pages})" if config.pages else ""
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else ""
            print(f"  {i}. '{config.text}' -> {config.color} (优先级: {config.priority}){pages_info}{context_info}")
        
        output_file2 = project_root / f"overlap_test_config_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file2}")
        
        result2 = highlighter2.process(str(output_file2))
        
        if result2['success']:
            print(f"✅ 处理成功！")
            print(f"总共添加 {result2['total_highlights']} 个高亮")
            print("\n各关键词处理结果:")
            for keyword, info in result2['keywords_processed'].items():
                print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
        else:
            print(f"❌ 处理失败: {result2['error']}")
        
        # 测试3：默认优先级测试
        print(f"\n=== 测试3：默认优先级测试 ===")
        highlighter3 = MultiKeywordHighlighter(str(test_file))
        
        # 不设置优先级，测试默认的长度优先机制
        highlighter3.add_keyword("苹果ipa应用提交审核", color="red")  # 8个字符，默认优先级最高
        highlighter3.add_keyword("ipa应用提交", color="blue")        # 6个字符
        highlighter3.add_keyword("应用提交", color="green")          # 4个字符
        highlighter3.add_keyword("提交", color="orange")            # 2个字符，默认优先级最低
        
        print("默认优先级配置（按长度）:")
        for i, config in enumerate(highlighter3.keyword_configs, 1):
            print(f"  {i}. '{config.text}' (长度: {len(config.text)}) -> {config.color} (优先级: {config.priority})")
        
        output_file3 = project_root / f"overlap_test_default_{test_file.name}"
        result3 = highlighter3.process(str(output_file3))
        
        if result3['success']:
            print(f"✅ 默认优先级测试成功！")
            print(f"总共添加 {result3['total_highlights']} 个高亮")
        else:
            print(f"❌ 默认优先级测试失败: {result3['error']}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 重叠处理功能测试完成！")
    return True


if __name__ == "__main__":
    test_overlap_handling()
    
    print("\n" + "="*70)
    print("重叠处理功能特点:")
    print("1. ✅ 默认按关键词长度设置优先级（长关键词优先）")
    print("2. ✅ 支持用户手动设置优先级覆盖默认行为")
    print("3. ✅ 自动检测和处理重叠区域")
    print("4. ✅ 高优先级关键词优先标记，低优先级的被跳过")
    print("5. ✅ 提供详细的处理统计信息")
    print("6. ✅ 完全向后兼容现有功能")
    print("\n配置示例:")
    print('{"text": "以供审核", "priority": 1, "color": "red"}')
    print('{"text": "审核", "priority": 2, "color": "blue"}')
    print("结果：只会标记'以供审核'，'审核'因重叠被跳过")
    print("="*70)
