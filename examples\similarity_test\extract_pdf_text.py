#!/usr/bin/env python3
"""
提取PDF文本内容用于调试
"""

import sys
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

import pymupdf


def extract_pdf_text():
    """提取PDF文本内容"""
    test_pdf = Path(__file__).parent / "test-file.pdf"
    
    if not test_pdf.exists():
        print(f"❌ PDF文件不存在: {test_pdf}")
        return
    
    try:
        doc = pymupdf.open(str(test_pdf))
        print(f"📄 PDF文件: {test_pdf}")
        print(f"📖 总页数: {len(doc)}")
        print("="*50)
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            print(f"\n--- 页面 {page_num + 1} ---")
            print(text[:500] + "..." if len(text) > 500 else text)
            
        doc.close()
        
    except Exception as e:
        print(f"❌ 提取PDF文本失败: {e}")


if __name__ == "__main__":
    extract_pdf_text()
