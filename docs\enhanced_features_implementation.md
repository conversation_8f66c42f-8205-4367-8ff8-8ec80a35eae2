# PDF高亮器功能增强实现文档

## 概述

本文档详细说明了对 `MultiKeywordHighlighter` 类实现的两个重要功能增强：

1. **API返回结果页面信息增强** - 为每个关键词添加页面信息字段
2. **关键词模糊匹配功能** - 支持基于开头和结尾字符的模糊匹配

## 功能1：API返回结果页面信息增强

### 功能描述

在 `process` 方法的返回结果中，为每个成功标注和失败标注的关键词都添加 `pages` 字段，记录关键词在PDF中的页面信息。

### 实现细节

#### 1.1 修改的核心方法

**`apply_highlights` 方法**
- 在关键词统计信息初始化时添加 `pages` 字段（使用set避免重复）
- 在处理每个高亮区域时收集页面信息
- 返回前将页面集合转换为排序的列表

```python
# 初始化时添加pages字段
keyword_stats[region.keyword_id] = {
    'id': region.keyword_id,
    'text': region.keyword,
    'pages': set()  # 新增字段
    # ... 其他字段
}

# 收集页面信息
keyword_stats[region.keyword_id]['pages'].add(region.page_num)

# 转换为排序列表
stats['pages'] = sorted(list(stats['pages']))
```

**`_generate_complete_stats` 方法**
- 修改方法签名，接收 `effective_pages_map` 参数
- 为失败的关键词添加尝试搜索的页面信息

```python
def _generate_complete_stats(self, processed_stats: Dict[str, Dict[str, Any]], 
                            effective_pages_map: Dict[str, Optional[List[int]]] = None):
    # 为失败关键词添加页面信息
    attempted_pages = []
    if effective_pages_map and config.text in effective_pages_map:
        attempted_pages = effective_pages_map[config.text] or []
```

#### 1.2 API返回结果结构

```json
{
  "keywords_by_id": {
    "keyword_id": {
      "id": "关键词ID",
      "text": "关键词文本", 
      "pages": [1, 2, 3],  // 新增：页面号码列表
      "highlights_added": 3,
      "status": "success"
    }
  }
}
```

## 功能2：关键词模糊匹配功能

### 功能描述

添加全局配置选项，允许用户通过配置文件开启关键词模糊匹配功能。用户可以设置提取关键词开头和结尾的字符数量，系统会生成相应的正则表达式进行匹配。

### 实现细节

#### 2.1 配置结构扩展

**KeywordConfig类扩展**
```python
@dataclass
class KeywordConfig:
    # ... 原有字段
    fuzzy_enabled: Optional[bool] = None  # 是否启用模糊匹配
    fuzzy_start_chars: Optional[int] = None  # 开头字符数
    fuzzy_end_chars: Optional[int] = None  # 结尾字符数
```

**MultiKeywordHighlighter类扩展**
```python
def __init__(self, pdf_path: str, context_fallback_strategy: str = "fallback_to_normal"):
    # ... 原有初始化
    # 模糊匹配全局配置
    self.fuzzy_matching_enabled = False
    self.fuzzy_start_chars = 2
    self.fuzzy_end_chars = 2
```

#### 2.2 核心实现方法

**`_should_use_fuzzy_matching` 方法**
- 判断是否应该对指定关键词使用模糊匹配
- 处理配置优先级：关键词级别 > 全局配置
- 排除已设置regex=True的关键词

**`_generate_fuzzy_regex` 方法**
- 生成模糊匹配的正则表达式
- 处理边界情况（关键词长度不足等）
- 自动转义特殊字符

```python
def _generate_fuzzy_regex(self, config: KeywordConfig) -> str:
    keyword = config.text
    start_chars = config.fuzzy_start_chars or self.fuzzy_start_chars
    end_chars = config.fuzzy_end_chars or self.fuzzy_end_chars

    if len(keyword) < start_chars + end_chars:
        return re.escape(keyword)  # 回退到精确匹配

    start_part = re.escape(keyword[:start_chars])
    end_part = re.escape(keyword[-end_chars:])
    # 使用 [\s\S]*? 来匹配包括换行符在内的任意字符，使用非贪婪匹配
    return f"{start_part}[\\s\\S]*?{end_part}"
```

**`_apply_fuzzy_matching` 方法**
- 应用模糊匹配，返回修改后的配置
- 创建新的配置副本，设置regex=True

#### 2.3 配置文件格式

```json
{
  "keywords": [
    {
      "text": "ABCDE",
      "color": "yellow",
      "fuzzy_enabled": true,
      "fuzzy_start_chars": 2,
      "fuzzy_end_chars": 2
    }
  ],
  "global_options": {
    "fuzzy_matching_enabled": true,
    "fuzzy_start_chars": 2,
    "fuzzy_end_chars": 2
  }
}
```

#### 2.4 模糊匹配示例

| 关键词 | 开头字符数 | 结尾字符数 | 生成正则表达式 | 匹配示例 |
|--------|------------|------------|----------------|----------|
| ABCDE | 2 | 2 | `AB[\s\S]*?DE` | ABCDE, AB123DE, AB任意内容DE, AB\n换行DE |
| 测试关键词 | 1 | 1 | `测[\s\S]*?词` | 测试关键词, 测试词, 测量关键词, 测\n试\n词 |
| 本书...教材 | 2 | 2 | `本书[\s\S]*?教材` | 支持跨行匹配，包含换行符的长文本 |

### 集成到搜索流程

在 `search_with_context` 方法中集成模糊匹配：

```python
def search_with_context(self, highlighter, config: KeywordConfig, effective_pages):
    # 应用模糊匹配（如果启用）
    search_config = self._apply_fuzzy_matching(config)
    
    # 使用处理后的配置进行搜索
    return highlighter.search_keywords(
        search_config.text,  # 可能是原始文本或正则表达式
        pages=effective_pages,
        case_sensitive=search_config.case_sensitive,
        regex=search_config.regex  # 模糊匹配时为True
    )
```

## 兼容性和边界情况处理

### 模糊匹配兼容性

1. **与现有regex功能共存**：已设置 `regex=True` 的关键词不会应用模糊匹配
2. **与上下文匹配结合**：模糊匹配可以与上下文短语功能结合使用
3. **自动回退**：关键词长度不足时自动回退到精确匹配

### 边界情况处理

1. **关键词长度验证**：`start_chars + end_chars` 不能超过关键词长度
2. **特殊字符转义**：使用 `re.escape()` 处理正则表达式特殊字符
3. **换行符支持**：使用 `[\s\S]*?` 替代 `.*` 来匹配包括换行符在内的任意字符
4. **非贪婪匹配**：使用 `*?` 进行非贪婪匹配，避免过度匹配
5. **配置验证**：验证字符数配置的有效性
6. **日志记录**：详细记录模糊匹配的应用过程

### 换行符处理说明

PDF文本提取时经常会遇到换行符问题，特别是长文本可能被分割成多行。为了解决这个问题：

- **问题**：传统的 `.*` 不匹配换行符，导致跨行文本无法匹配
- **解决方案**：使用 `[\s\S]*?` 匹配包括换行符在内的任意字符
- **示例**：
  ```
  原文本：本书可以作为高校
          教育技术学专业教材

  关键词：本书可以作为高校教育技术学专业教材
  配置：开头2字符，结尾2字符
  生成正则：本书[\s\S]*?教材
  匹配结果：✅ 成功匹配跨行文本
  ```

### 长关键词处理优化

对于特别长的关键词（如60+字符），系统进行了特殊优化：

1. **上下文验证优化**：
   - 精确匹配：要求完整关键词存在于上下文中
   - 模糊匹配：只要求开头和结尾字符存在于上下文中

2. **示例场景**：
   ```
   长关键词：本书可以作为高校教育技术学专业教材、大中小学教师继续教育培训用书，也可作为教育信息化从业人员自主学习教育技术

   配置：开头2字符，结尾4字符
   生成正则：本书[\s\S]*?教育技术

   验证逻辑：检查文本中是否包含"本书"和"教育技术"
   ```

3. **调试日志增强**：
   ```
   INFO - 模糊匹配详情 - 关键词: '长关键词...' (长度: 65)
   INFO -   开头 2 字符: '本书'
   INFO -   结尾 4 字符: '教育技术'
   INFO -   生成正则表达式: '本书[\s\S]*?教育技术'
   DEBUG - 模糊匹配验证：检查开头字符 '本书' 和结尾字符 '教育技术'
   ```

## 使用示例

### Python API使用

```python
# 基本使用
highlighter = MultiKeywordHighlighter('document.pdf')
highlighter.load_config_file('enhanced_features_config.json')
result = highlighter.process()

# 检查页面信息
for keyword_id, stats in result['keywords_by_id'].items():
    print(f'关键词: {stats["text"]}')
    print(f'标记页面: {stats["pages"]}')
    print(f'状态: {stats["status"]}')

# 编程方式添加模糊匹配关键词
highlighter.add_keyword(
    text='编程关键词',
    color='pink',
    fuzzy_enabled=True,
    fuzzy_start_chars=2,
    fuzzy_end_chars=2
)
```

### 配置文件示例

参见 `examples/enhanced_features_config.json` 文件，包含完整的配置示例和说明。

## 总结

这两个功能增强显著提升了PDF高亮器的实用性：

1. **页面信息增强**使用户能够准确了解每个关键词的标记位置
2. **模糊匹配功能**提供了更灵活的关键词匹配方式，适应各种文本变化场景

所有功能都保持了向后兼容性，不会影响现有的使用方式。
