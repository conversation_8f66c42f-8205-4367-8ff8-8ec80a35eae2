#!/usr/bin/env python3
"""
上下文短语不存在时的处理策略测试脚本

测试当配置的上下文短语在PDF文件中不存在时的不同处理策略：
1. fallback_to_normal: 降级为普通关键字搜索（默认）
2. skip: 跳过该关键字
3. warn_only: 仅记录警告，返回空结果
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置详细日志
logging.basicConfig(level=logging.DEBUG)
logger = get_logger()


def create_context_not_found_configs():
    """创建包含不存在上下文短语的测试配置文件"""
    
    # 配置1：默认策略（降级为普通搜索）
    config1 = {
        "keywords": [
            {
                "text": "审核",
                "color": "red",
                "context_phrase": "这个上下文短语不存在于PDF中"
            },
            {
                "text": "提交",
                "color": "blue", 
                "context_phrase": "另一个不存在的上下文短语"
            },
            {
                "text": "应用",
                "color": "green",
                "context_phrase": "苹果ipa应用"  # 这个存在
            },
            {
                "text": "普通关键词",
                "color": "yellow"
            }
        ],
        "global_options": {
            "context_fallback_strategy": "fallback_to_normal",
            "output_suffix": "_fallback_normal"
        }
    }
    
    # 配置2：跳过策略
    config2 = {
        "keywords": [
            {
                "text": "审核",
                "color": "red",
                "context_phrase": "这个上下文短语不存在于PDF中"
            },
            {
                "text": "提交",
                "color": "blue",
                "context_phrase": "另一个不存在的上下文短语"
            },
            {
                "text": "应用",
                "color": "green",
                "context_phrase": "苹果ipa应用"  # 这个存在
            },
            {
                "text": "普通关键词",
                "color": "yellow"
            }
        ],
        "global_options": {
            "context_fallback_strategy": "skip",
            "output_suffix": "_skip"
        }
    }
    
    # 配置3：仅警告策略
    config3 = {
        "keywords": [
            {
                "text": "审核",
                "color": "red",
                "context_phrase": "这个上下文短语不存在于PDF中"
            },
            {
                "text": "提交",
                "color": "blue",
                "context_phrase": "另一个不存在的上下文短语"
            },
            {
                "text": "应用",
                "color": "green",
                "context_phrase": "苹果ipa应用"  # 这个存在
            },
            {
                "text": "普通关键词",
                "color": "yellow"
            }
        ],
        "global_options": {
            "context_fallback_strategy": "warn_only",
            "output_suffix": "_warn_only"
        }
    }
    
    configs = [
        ("fallback_normal_config.json", config1),
        ("skip_config.json", config2),
        ("warn_only_config.json", config3)
    ]
    
    config_files = []
    for filename, config in configs:
        config_file = project_root / "examples" / filename
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        config_files.append(config_file)
        print(f"创建配置文件: {config_file}")
    
    return config_files


def test_context_not_found_strategies():
    """测试上下文短语不存在时的不同处理策略"""
    print("=== 上下文短语不存在时的处理策略测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 创建测试配置文件
    config_files = create_context_not_found_configs()
    
    strategies = [
        ("fallback_to_normal", "降级为普通搜索"),
        ("skip", "跳过关键字"),
        ("warn_only", "仅记录警告")
    ]
    
    results = []
    
    for i, (strategy, description) in enumerate(strategies):
        print(f"\n=== 测试策略 {i+1}: {strategy} ({description}) ===")
        
        try:
            # 使用对应的配置文件
            highlighter = MultiKeywordHighlighter(str(test_file))
            highlighter.load_config_file(str(config_files[i]))
            
            print(f"降级策略: {highlighter.context_fallback_strategy}")
            
            # 显示配置
            print("配置的关键词:")
            for j, config in enumerate(highlighter.keyword_configs, 1):
                context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文)"
                print(f"  {j}. '{config.text}' -> {config.color}{context_info}")
            
            # 执行处理
            output_file = project_root / f"context_not_found_{strategy}_{test_file.name}"
            print(f"\n开始处理，输出文件: {output_file}")
            
            result = highlighter.process(str(output_file))
            
            if result['success']:
                print(f"✅ 处理成功！")
                print(f"总共添加 {result['total_highlights']} 个高亮")
                print("\n各关键词处理结果:")
                for keyword, info in result['keywords_processed'].items():
                    print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
                
                results.append({
                    'strategy': strategy,
                    'total_highlights': result['total_highlights'],
                    'keywords_processed': len(result['keywords_processed'])
                })
            else:
                print(f"❌ 处理失败: {result['error']}")
                results.append({
                    'strategy': strategy,
                    'total_highlights': 0,
                    'keywords_processed': 0
                })
        
        except Exception as e:
            logger.error(f"策略 {strategy} 测试失败: {e}")
            import traceback
            traceback.print_exc()
            results.append({
                'strategy': strategy,
                'total_highlights': 0,
                'keywords_processed': 0
            })
    
    # 测试编程API方式
    print(f"\n=== 编程API方式测试 ===")
    
    api_strategies = ["fallback_to_normal", "skip", "warn_only"]
    
    for strategy in api_strategies:
        print(f"\n测试API策略: {strategy}")
        
        try:
            highlighter = MultiKeywordHighlighter(str(test_file), context_fallback_strategy=strategy)
            
            # 添加包含不存在上下文的关键词
            highlighter.add_keyword("审核", color="red", context_phrase="这个上下文不存在")
            highlighter.add_keyword("应用", color="green", context_phrase="苹果ipa应用")  # 存在的
            highlighter.add_keyword("普通", color="blue")  # 无上下文
            
            result = highlighter.process()
            
            if result['success']:
                print(f"  ✅ API策略 {strategy} 成功，添加 {result['total_highlights']} 个高亮")
            else:
                print(f"  ❌ API策略 {strategy} 失败")
        
        except Exception as e:
            print(f"  ❌ API策略 {strategy} 异常: {e}")
    
    # 结果对比
    print(f"\n=== 策略对比结果 ===")
    print("| 策略 | 总高亮数 | 处理关键词数 | 说明 |")
    print("|------|----------|--------------|------|")
    
    for result in results:
        strategy = result['strategy']
        highlights = result['total_highlights']
        keywords = result['keywords_processed']
        
        if strategy == "fallback_to_normal":
            desc = "不存在的上下文降级为普通搜索"
        elif strategy == "skip":
            desc = "不存在的上下文关键词被跳过"
        else:
            desc = "不存在的上下文关键词不处理"
        
        print(f"| {strategy} | {highlights} | {keywords} | {desc} |")
    
    print("\n✅ 上下文短语不存在时的处理策略测试完成！")
    return True


if __name__ == "__main__":
    test_context_not_found_strategies()
    
    print("\n" + "="*70)
    print("处理策略说明:")
    print("1. fallback_to_normal: 当上下文短语不存在时，降级为普通关键字搜索")
    print("2. skip: 当上下文短语不存在时，跳过该关键字，不进行任何处理")
    print("3. warn_only: 当上下文短语不存在时，仅记录警告，不进行搜索")
    print("\n推荐使用 fallback_to_normal 策略，既保证了功能可用性，又提供了警告信息")
    print("="*70)
