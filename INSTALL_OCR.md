# OCR功能安装指南

本指南将帮助您完成PDF高亮器OCR功能的完整安装。

## 📋 安装检查

首先运行检查脚本确认当前状态：

```bash
uv run python test_ocr_setup.py
```

## 🔧 安装步骤

### 1. 安装Python依赖

```bash
# 使用uv安装
uv add pytesseract Pillow

# 或使用pip安装
pip install pytesseract Pillow
```

### 2. 安装Tesseract OCR引擎

#### Windows 安装

1. **下载Tesseract安装包**
   - 访问：https://github.com/UB-Mannheim/tesseract/wiki
   - 下载最新版本的Windows安装包（推荐64位版本）

2. **运行安装程序**
   - 双击下载的安装包
   - **重要**：在安装过程中选择"Additional language data"
   - 确保勾选"Chinese (Simplified)"和"Chinese (Traditional)"
   - 记住安装路径（通常是 `C:\Program Files\Tesseract-OCR`）

3. **配置环境变量**
   - 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
   - 在"系统变量"中找到"Path"，点击"编辑"
   - 点击"新建"，添加Tesseract安装路径：`C:\Program Files\Tesseract-OCR`
   - 点击"确定"保存

4. **验证安装**
   ```cmd
   # 打开新的命令提示符窗口
   tesseract --version
   ```

#### macOS 安装

```bash
# 使用Homebrew安装
brew install tesseract

# 安装语言包
brew install tesseract-lang

# 验证安装
tesseract --version
tesseract --list-langs
```

#### Ubuntu/Debian 安装

```bash
# 更新包列表
sudo apt-get update

# 安装Tesseract和中文语言包
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-chi-tra

# 验证安装
tesseract --version
tesseract --list-langs
```

#### CentOS/RHEL 安装

```bash
# 安装EPEL仓库
sudo yum install epel-release

# 安装Tesseract
sudo yum install tesseract tesseract-langpack-chi_sim tesseract-langpack-chi_tra

# 验证安装
tesseract --version
tesseract --list-langs
```

### 3. 验证完整安装

运行检查脚本确认所有组件都已正确安装：

```bash
uv run python test_ocr_setup.py
```

应该看到所有项目都显示 ✓。

## 🚀 测试OCR功能

### 基本测试

```bash
# 测试OCR高亮功能
uv run pdf-highlight test.pdf "关键词" --enable-ocr

# 仅搜索图片中的文字
uv run pdf-highlight test.pdf "图片文字" --ocr-only

# 指定中文识别
uv run pdf-highlight test.pdf "中文" --enable-ocr --ocr-lang chi_sim
```

### Python API测试

```python
from pdf_highlighter import PDFHighlighter

# 测试OCR功能
with PDFHighlighter('test.pdf', enable_ocr=True) as highlighter:
    results = highlighter.search_keywords('测试', enable_ocr=True)
    print(f"找到 {sum(len(quads) for _, quads in results)} 个匹配项")
```

## 🔧 故障排除

### 常见问题

#### 1. "tesseract is not installed or it's not in your PATH"

**解决方案：**
- 确保Tesseract已正确安装
- 检查PATH环境变量是否包含Tesseract路径
- 重启命令行窗口或IDE
- Windows用户可能需要重启计算机

#### 2. "TesseractNotFoundError"

**Windows解决方案：**
```python
# 在代码中手动指定tesseract路径
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

#### 3. "Language 'chi_sim' is not available"

**解决方案：**
- 确保安装时选择了中文语言包
- 重新安装Tesseract并选择语言包
- 检查tessdata目录是否包含chi_sim.traineddata文件

#### 4. OCR识别精度低

**优化建议：**
```python
from pdf_highlighter.config.settings import set_config

# 调整OCR参数
set_config('ocr.confidence_threshold', 50)  # 降低置信度阈值
set_config('ocr.image_preprocessing.scale_factor', 3.0)  # 增加放大倍数
set_config('ocr.image_preprocessing.enable_denoising', True)  # 启用去噪
```

#### 5. OCR处理速度慢

**优化建议：**
```python
# 启用并行处理
set_config('ocr.parallel_processing', True)
set_config('ocr.max_workers', 4)

# 启用缓存
set_config('ocr.cache_enabled', True)
```

### 手动测试Tesseract

```bash
# 创建测试图片（如果有的话）
tesseract test_image.png output -l chi_sim+eng

# 检查支持的语言
tesseract --list-langs

# 检查版本信息
tesseract --version
```

## 📁 文件位置

### Windows
- **Tesseract安装目录**: `C:\Program Files\Tesseract-OCR\`
- **语言数据目录**: `C:\Program Files\Tesseract-OCR\tessdata\`
- **配置文件**: 项目目录下的配置文件

### macOS
- **Tesseract安装目录**: `/usr/local/bin/tesseract`
- **语言数据目录**: `/usr/local/share/tessdata/`

### Linux
- **Tesseract安装目录**: `/usr/bin/tesseract`
- **语言数据目录**: `/usr/share/tesseract-ocr/tessdata/`

## 🎯 完成确认

安装完成后，您应该能够：

1. ✅ 运行 `tesseract --version` 显示版本信息
2. ✅ 运行 `tesseract --list-langs` 显示包含 `chi_sim` 和 `eng`
3. ✅ 运行 `uv run python test_ocr_setup.py` 所有项目显示 ✓
4. ✅ 运行 `uv run pdf-highlight test.pdf "关键词" --enable-ocr` 成功执行

## 📞 获取帮助

如果遇到问题：

1. 首先运行 `uv run python test_ocr_setup.py` 查看具体问题
2. 检查本指南的故障排除部分
3. 确认Tesseract版本是否为最新版本
4. 查看项目的 `examples/OCR_GUIDE.md` 获取更多使用示例

## 🔗 相关链接

- [Tesseract官方文档](https://tesseract-ocr.github.io/)
- [Tesseract Windows安装包](https://github.com/UB-Mannheim/tesseract/wiki)
- [pytesseract文档](https://pypi.org/project/pytesseract/)
- [Pillow文档](https://pillow.readthedocs.io/)

安装完成后，您就可以享受强大的PDF图片文字识别和高亮功能了！
