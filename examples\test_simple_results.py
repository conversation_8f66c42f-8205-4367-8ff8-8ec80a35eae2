#!/usr/bin/env python3
"""
简化结果测试脚本

测试简化后的返回结果：
- 只关注哪些关键词被高亮了
- 哪些关键词没有被高亮
- 简洁的状态信息
"""

import sys
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = get_logger()


def create_simple_test_config():
    """创建简单测试配置文件"""
    config = {
        "keywords": [
            {
                "id": "review_keyword",
                "text": "审核",
                "color": "red",
                "opacity": 0.8
            },
            {
                "id": "submit_keyword",
                "text": "提交",
                "color": "blue",
                "opacity": 0.7
            },
            {
                "id": "app_keyword",
                "text": "应用",
                "color": "green",
                "opacity": 0.6,
                "context_phrase": "苹果ipa应用"
            },
            {
                "id": "not_found",
                "text": "这个关键词不存在",
                "color": "purple",
                "opacity": 0.5
            }
        ],
        "global_options": {
            "output_suffix": "_simple_test"
        }
    }
    
    config_file = project_root / "examples" / "simple_test_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    return config_file


def test_simple_results():
    """测试简化的结果格式"""
    print("=== 简化结果测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 方式1：配置文件测试
        print("\n=== 配置文件方式 ===")
        config_file = create_simple_test_config()
        
        highlighter1 = MultiKeywordHighlighter(str(test_file))
        highlighter1.load_config_file(str(config_file))
        
        result1 = highlighter1.process()
        
        if result1['success']:
            print("✅ 处理成功")
            
            # 显示简单的成功/失败状态
            print("\n📋 关键词处理结果:")
            successful_keywords = []
            failed_keywords = []
            
            for keyword_id, stats in result1['keywords_by_id'].items():
                if stats['highlights_added'] > 0:
                    successful_keywords.append(stats['text'])
                    print(f"  ✅ [{keyword_id}] '{stats['text']}' -> 高亮了 {stats['highlights_added']} 次")
                else:
                    failed_keywords.append(stats['text'])
                    print(f"  ❌ [{keyword_id}] '{stats['text']}' -> 没有被高亮 ({stats['status']})")
            
            # 显示统计摘要
            summary = result1['summary']
            print(f"\n📊 统计摘要:")
            print(f"  总关键词: {summary['total_keywords']}")
            print(f"  成功高亮: {summary['successful_keywords']} 个")
            print(f"  未被高亮: {summary['failed_keywords']} 个")
            print(f"  总高亮数: {result1['total_highlights']}")
            
        else:
            print(f"❌ 处理失败: {result1['error']}")
        
        # 方式2：编程API测试
        print(f"\n=== 编程API方式 ===")
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        
        # 添加关键词
        id1 = highlighter2.add_keyword("审核", color="red", id="api_review")
        id2 = highlighter2.add_keyword("应用", color="green", id="api_app", context_phrase="苹果ipa应用")
        id3 = highlighter2.add_keyword("不存在", color="blue", id="api_not_found")
        
        result2 = highlighter2.process()
        
        if result2['success']:
            print("✅ API处理成功")
            
            # 检查特定关键词的状态
            print("\n🔍 特定关键词状态检查:")
            for keyword_id in [id1, id2, id3]:
                if keyword_id in result2['keywords_by_id']:
                    stats = result2['keywords_by_id'][keyword_id]
                    status = "成功高亮" if stats['highlights_added'] > 0 else "未被高亮"
                    print(f"  [{keyword_id}] '{stats['text']}': {status}")
            
            # 简单的成功/失败列表
            print(f"\n📝 简单总结:")
            successful = [stats['text'] for stats in result2['keywords_by_id'].values() 
                         if stats['highlights_added'] > 0]
            failed = [stats['text'] for stats in result2['keywords_by_id'].values() 
                     if stats['highlights_added'] == 0]
            
            print(f"  成功的关键词: {successful}")
            print(f"  失败的关键词: {failed}")
        
        else:
            print(f"❌ API处理失败: {result2['error']}")
        
        # 方式3：结果对比
        print(f"\n=== 结果对比 ===")
        if result1['success'] and result2['success']:
            print(f"配置文件方式: {result1['total_highlights']} 个高亮")
            print(f"编程API方式: {result2['total_highlights']} 个高亮")
            
            # 验证结果结构的简洁性
            print(f"\n🔍 结果结构验证:")
            
            # 检查是否包含复杂的详细信息
            sample_keyword = list(result1['keywords_by_id'].values())[0]
            complex_fields = ['all_matches', 'highlights_applied', 'highlights_failed', 'bbox', 'context_text']
            
            has_complex_fields = any(field in sample_keyword for field in complex_fields)
            
            if not has_complex_fields:
                print("✅ 结果结构简洁，只包含必要的状态信息")
            else:
                print("⚠️ 结果结构包含复杂字段")
            
            # 显示关键字段
            essential_fields = ['id', 'text', 'matches_found', 'highlights_added', 'status']
            missing_fields = [field for field in essential_fields if field not in sample_keyword]
            
            if not missing_fields:
                print("✅ 包含所有必要的状态字段")
            else:
                print(f"❌ 缺少必要字段: {missing_fields}")
    
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 简化结果测试完成！")
    return True


def demo_simple_usage():
    """演示简单的使用方式"""
    print(f"\n=== 简单使用演示 ===")
    
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        return
    
    test_file = test_files[0]
    
    # 最简单的使用方式
    highlighter = MultiKeywordHighlighter(str(test_file))
    highlighter.add_keyword("审核", color="red")
    highlighter.add_keyword("应用", color="green")
    highlighter.add_keyword("不存在的词", color="blue")
    
    result = highlighter.process()
    
    print("最简单的结果检查:")
    if result['success']:
        for keyword_id, stats in result['keywords_by_id'].items():
            status = "✅ 被高亮了" if stats['highlights_added'] > 0 else "❌ 没有被高亮"
            print(f"  '{stats['text']}': {status}")
    else:
        print("处理失败")


if __name__ == "__main__":
    test_simple_results()
    demo_simple_usage()
    
    print("\n" + "="*60)
    print("简化结果的优势:")
    print("1. ✅ 清晰的成功/失败状态")
    print("2. ✅ 简洁的数据结构")
    print("3. ✅ 易于理解和使用")
    print("4. ✅ 专注于核心需求：哪些被高亮了")
    print("5. ✅ 减少不必要的复杂信息")
    print("="*60)
