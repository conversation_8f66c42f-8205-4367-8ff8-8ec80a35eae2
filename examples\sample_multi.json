{"keywords": [{"text": "前端开发", "color": "yellow", "opacity": 0.6, "case_sensitive": false, "regex": false, "pages": "1-5"}, {"text": "后端开发", "color": "blue", "opacity": 0.7, "case_sensitive": false, "regex": false, "pages": "6-10"}, {"text": "全栈工程师", "color": "green", "opacity": 0.5, "case_sensitive": false, "regex": false}, {"text": "Python|Java|JavaScript", "color": "orange", "opacity": 0.8, "case_sensitive": false, "regex": true, "pages": "1,3,5-7"}, {"text": "API", "color": "#FF6B6B", "opacity": 0.6, "case_sensitive": true, "regex": false, "whole_word": true, "pages": "2,4,8-10"}], "global_options": {"pages": null, "occurrence": null, "whole_word": false, "overwrite": false, "output_suffix": "_multi_highlighted"}, "processing": {"max_workers": 4, "recursive": true, "skip_existing": true}, "logging": {"level": "INFO", "file": "multi_highlight.log", "console": true}}