# PDF关键词高亮工具 (PDF Highlighter)

[![Python Version](https://img.shields.io/badge/python-3.8.1%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![PyMuPDF](https://img.shields.io/badge/powered%20by-PyMuPDF-red.svg)](https://pymupdf.readthedocs.io/)

一个基于PyMuPDF的PDF文件关键词搜索和高亮注释工具，支持多格式PDF文件处理，提供强大的命令行接口和Python API。

## ✨ 主要特性

- 🔍 **智能搜索**：支持普通文本、正则表达式、大小写敏感、完整单词匹配
- 🖼️ **OCR图片识别**：识别PDF中图片内的文字并进行高亮标注（支持中文）
- 🎨 **自定义高亮**：支持多种颜色格式和透明度设置
- 🌈 **多关键词高亮**：一次执行支持多个关键词使用不同颜色高亮
- 📄 **多格式支持**：处理Word、Excel、图片转换等各种来源生成的PDF文件
- 🎯 **精确定位**：支持页码范围和关键词序号过滤
- ⚙️ **配置文件支持**：JSON配置文件实现复杂的多关键词配置
- 💻 **命令行友好**：简洁易用的CLI接口，支持批处理
- 🐍 **Python API**：完整的编程接口，易于集成到其他项目
- 🛡️ **安全可靠**：原始文件保持不变，完善的错误处理
- 🌐 **中文支持**：完整的中文界面和错误提示

## 📦 安装

### 使用uv（推荐）

```bash
# 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装PDF高亮工具
uv add pdf-highlighter
```

### 使用pip

```bash
# 基本安装
pip install pdf-highlighter

# 包含OCR功能
pip install pdf-highlighter[ocr]
```

### 从源码安装

```bash
git clone https://github.com/jiwei/pdf-highlighter.git
cd pdf-highlighter
uv sync
```

## 🚀 快速开始

### 快速体验

```bash
# 1. 基本高亮
pdf-highlight document.pdf "关键词" -o highlighted.pdf

# 2. 多关键词不同颜色
pdf-highlight document.pdf --keywords "前端" "后端" --colors yellow blue

# 3. 创建配置文件模板
pdf-highlight --create-config my_config.json

# 4. 使用配置文件（推荐）
pdf-highlight document.pdf --config my_config.json
```

### 命令行使用

#### 基础用法
```bash
# 基本高亮
pdf-highlight input.pdf "关键词"

# 指定输出文件和颜色
pdf-highlight input.pdf "Python" -c red -a 0.8 -o output.pdf

# 页面范围限制
pdf-highlight input.pdf "数据分析" -p 1-3,5,7-10

# 正则表达式搜索
pdf-highlight input.pdf "前端|后端" --regex
```

#### 多关键词高亮
```bash
# 命令行模式
pdf-highlight input.pdf --keywords "前端" "后端" "工程师" --colors yellow blue green

# 配置文件模式（推荐）
pdf-highlight input.pdf --config config.json

# 创建配置文件模板
pdf-highlight --create-config sample_config.json
```

#### OCR功能
```bash
# 搜索图片中的文字
pdf-highlight input.pdf "图片文字" --enable-ocr

# 仅搜索图片内容
pdf-highlight input.pdf "图片内容" --ocr-only

# 指定OCR语言
pdf-highlight input.pdf "中文内容" --enable-ocr --ocr-lang chi_sim
```

### Python API使用

#### 单关键词高亮
```python
from pdf_highlighter import PDFHighlighter

# 基本使用
with PDFHighlighter('document.pdf') as highlighter:
    # 搜索关键词
    results = highlighter.search_keywords('关键词')

    # 添加高亮
    highlighter.add_highlights(results, color=(1, 1, 0), opacity=0.5)

    # 保存文件
    highlighter.save_pdf('highlighted.pdf')

# 高级搜索
with PDFHighlighter('document.pdf') as highlighter:
    # 正则表达式搜索
    results = highlighter.search_keywords(
        r'前端|后端',
        regex=True,
        case_sensitive=False
    )

    # 只高亮第一个匹配项
    highlighter.add_highlights(results, occurrence=1)
    highlighter.save_pdf('output.pdf')

# OCR功能：搜索图片中的文字
with PDFHighlighter('document.pdf', enable_ocr=True) as highlighter:
    # 搜索图片中的文字
    results = highlighter.search_keywords('图片文字', enable_ocr=True)

    # 仅搜索图片中的文字
    image_results = highlighter.search_in_images_only('图片内容')

    # 添加高亮
    highlighter.add_highlights(results, color=(1, 1, 0))
    highlighter.add_highlights(image_results, color=(0, 1, 0))
    highlighter.save_pdf('ocr_highlighted.pdf')
```

#### 多关键词高亮
```python
from pdf_highlighter import MultiKeywordHighlighter

# 方式1：编程API
highlighter = MultiKeywordHighlighter('document.pdf')

# 添加多个关键词，每个使用不同颜色
highlighter.add_keyword('前端', color='yellow', opacity=0.6)
highlighter.add_keyword('后端', color='blue', opacity=0.7)
highlighter.add_keyword('工程师', color='green', opacity=0.5)

# 执行处理
result = highlighter.process('output.pdf')
print(f"总共添加 {result['total_highlights']} 个高亮")

# 方式2：使用配置文件
highlighter = MultiKeywordHighlighter('document.pdf')
highlighter.load_config_file('config.json')
result = highlighter.process()
```

## 📖 详细使用说明

### 命令行参数

#### 基本参数
| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `input_file` | - | 输入PDF文件路径 | `document.pdf` |
| `keyword` | - | 搜索关键词（单关键词模式） | `"关键词"` |
| `--output` | `-o` | 输出文件路径 | `-o output.pdf` |
| `--pages` | `-p` | 页码范围 | `-p 1,3-5,7` |
| `--occurrence` | `-n` | 关键词序号 | `-n 2` |
| `--color` | `-c` | 高亮颜色 | `-c red` |
| `--opacity` | `-a` | 透明度(0-1) | `-a 0.8` |
| `--case-sensitive` | - | 区分大小写 | `--case-sensitive` |
| `--regex` | - | 正则表达式模式 | `--regex` |
| `--whole-word` | - | 完整单词匹配 | `--whole-word` |
| `--dry-run` | - | 预览模式 | `--dry-run` |
| `--verbose` | `-v` | 详细输出 | `-v` |

#### OCR参数
| 参数 | 说明 | 示例 |
|------|------|------|
| `--enable-ocr` | 启用OCR功能，搜索图片中的文字 | `--enable-ocr` |
| `--ocr-only` | 仅搜索图片中的文字，不搜索PDF文本 | `--ocr-only` |
| `--ocr-lang` | OCR识别语言设置 | `--ocr-lang chi_sim+eng` |
| `--ocr-confidence` | OCR置信度阈值(0-100) | `--ocr-confidence 70` |

#### 多关键词参数
| 参数 | 说明 | 示例 |
|------|------|------|
| `--keywords` | 多个关键词列表 | `--keywords "前端" "后端" "工程师"` |
| `--colors` | 对应的颜色列表 | `--colors yellow blue green` |
| `--config` | 配置文件路径 | `--config config.json` |
| `--create-config` | 创建配置文件模板 | `--create-config sample.json` |

### 颜色格式支持

```bash
# 颜色名称
pdf-highlight input.pdf "text" -c yellow
pdf-highlight input.pdf "text" -c red

# 十六进制
pdf-highlight input.pdf "text" -c "#FF0000"
pdf-highlight input.pdf "text" -c "#00FF00"

# RGB值
pdf-highlight input.pdf "text" -c "255,0,0"
pdf-highlight input.pdf "text" -c "0,255,0"
```

### 页码范围格式

```bash
# 单页
pdf-highlight input.pdf "text" -p 1

# 页码范围
pdf-highlight input.pdf "text" -p 1-5

# 多个页码和范围
pdf-highlight input.pdf "text" -p 1,3-5,7,10-12
```

### 配置文件格式

创建配置文件模板：
```bash
pdf-highlight --create-config my_config.json
```

#### 完整配置文件示例

```json
{
  "keywords": [
    {
      "text": "前端开发",
      "color": "yellow",
      "opacity": 0.6,
      "case_sensitive": false,
      "regex": false,
      "pages": "1-5",
      "priority": 1
    },
    {
      "text": "后端开发",
      "color": "blue",
      "opacity": 0.7,
      "pages": "6-10",
      "priority": 1
    },
    {
      "text": "审核",
      "color": "red",
      "opacity": 0.8,
      "pages": "3",
      "context_phrase": "添加以供审核",
      "priority": 1
    },
    {
      "text": "审核",
      "color": "orange",
      "opacity": 0.7,
      "pages": "2",
      "context_phrase": "添加审核信息",
      "priority": 1
    },
    {
      "text": "审核",
      "color": "yellow",
      "opacity": 0.6,
      "pages": "1",
      "priority": 2
    },
    {
      "text": "Python|Java|JavaScript",
      "color": "green",
      "opacity": 0.8,
      "regex": true,
      "pages": "1,3,5-7"
    },
    {
      "text": "API",
      "color": "#FF6B6B",
      "opacity": 0.6,
      "case_sensitive": true,
      "whole_word": true,
      "occurrence": 5
    }
  ],
  "global_options": {
    "pages": null,
    "occurrence": null,
    "whole_word": false,
    "output_suffix": "_highlighted"
  }
}
```

#### 配置字段说明

**关键词配置 (keywords)**：
| 字段 | 类型 | 必需 | 说明 | 示例 |
|------|------|------|------|------|
| `id` | string | ❌ | 关键词唯一标识符 | `"frontend_dev"` |
| `text` | string | ✅ | 要搜索的关键词文本 | `"前端开发"` |
| `color` | string/array | ❌ | 高亮颜色 | `"red"`, `"#FF0000"`, `[1,0,0]` |
| `opacity` | number | ❌ | 透明度 (0-1) | `0.6` |
| `pages` | string | ❌ | 页面范围 | `"1,3-5,7"` |
| `context_phrase` | string | ❌ | 上下文短语 | `"添加以供审核"` |
| `similarity_threshold` | number | ❌ | 上下文相似度阈值 (0-1) | `0.8` |
| `priority` | number | ❌ | 优先级 (数字越小优先级越高) | `1` |
| `case_sensitive` | boolean | ❌ | 是否区分大小写 | `false` |
| `regex` | boolean | ❌ | 是否启用正则表达式 | `false` |
| `whole_word` | boolean | ❌ | 是否完整单词匹配 | `false` |
| `occurrence` | number | ❌ | 只高亮第几个匹配项 | `5` |
| `annotation_style` | string | ❌ | 标记样式 | `"highlight"`, `"underline"`, `"strikeout"`, `"squiggly"` |

**全局选项 (global_options)**：
| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `pages` | string | 全局页面范围 | `"1-10"` |
| `occurrence` | number | 全局匹配项限制 | `1` |
| `whole_word` | boolean | 全局完整单词匹配 | `false` |
| `context_similarity_threshold` | number | 全局上下文相似度阈值 (0-1) | `0.8` |
| `context_fallback_strategy` | string | 上下文降级策略 | `"fallback_to_normal"` |
| `output_suffix` | string | 输出文件后缀 | `"_highlighted"` |

## 🚀 高级功能特性

### 1. 页面级关键字配置

为每个关键字单独指定生效的页面范围，实现精确控制：

```json
{
  "text": "重要概念",
  "color": "red",
  "pages": "1-3"    // 只在第1-3页生效
}
```

**页面范围格式**：
- `"1"` - 单页
- `"1-5"` - 页码范围
- `"1,3,5"` - 多个单页
- `"1-3,5,7-10"` - 混合格式

### 2. 上下文相关匹配

在特定上下文短语中精确匹配关键词，避免误匹配：

```json
{
  "text": "审核",
  "color": "red",
  "context_phrase": "添加以供审核"    // 只在此短语中高亮"审核"
}
```

### 3. 重叠区域智能处理

自动处理重叠关键词，避免重复标记：

```json
{
  "keywords": [
    {
      "text": "以供审核",
      "color": "red",
      "priority": 1        // 高优先级
    },
    {
      "text": "审核",
      "color": "blue",
      "priority": 2        // 低优先级，重叠时被跳过
    }
  ]
}
```

**智能处理机制**：
- 🎯 **优先级排序**：数字越小优先级越高
- 📏 **默认优先级**：按关键词长度自动设置（长关键词优先）
- 🎯 **冲突检测**：自动检测重叠区域
- ⚙️ **用户可控**：支持手动设置优先级

### 4. 多重配置组合

同一关键词可在不同页面、不同上下文中使用不同效果：

```json
{
  "keywords": [
    {
      "text": "审核",
      "color": "red",
      "pages": "3",
      "context_phrase": "添加以供审核",
      "priority": 1
    },
    {
      "text": "审核",
      "color": "blue",
      "pages": "2",
      "context_phrase": "添加审核信息",
      "priority": 1
    },
    {
      "text": "审核",
      "color": "yellow",
      "pages": "1",
      "priority": 2
    }
  ]
}
```

**效果**：
- 第3页"添加以供审核"中的"审核" → 红色
- 第2页"添加审核信息"中的"审核" → 蓝色
- 第1页其他位置的"审核" → 黄色

## 💡 完整使用示例

### 示例1：合同文档精确标注

**需求**：在合同文档中精确标注不同类型的"审核"关键词

```json
{
  "keywords": [
    {
      "text": "审核",
      "color": "red",
      "opacity": 0.8,
      "pages": "3",
      "context_phrase": "添加以供审核",
      "priority": 1
    },
    {
      "text": "审核",
      "color": "blue",
      "opacity": 0.7,
      "pages": "2",
      "context_phrase": "内部审核流程",
      "priority": 1
    },
    {
      "text": "审核",
      "color": "yellow",
      "opacity": 0.6,
      "pages": "1,4-6",
      "priority": 2
    },
    {
      "text": "甲方|乙方",
      "color": "green",
      "opacity": 0.5,
      "regex": true
    }
  ],
  "global_options": {
    "output_suffix": "_contract_highlighted"
  }
}
```

```bash
pdf-highlight contract.pdf --config contract_config.json
```

**效果**：
- 第3页"添加以供审核"中的"审核" → 红色高亮
- 第2页"内部审核流程"中的"审核" → 蓝色高亮
- 第1页和第4-6页其他位置的"审核" → 黄色高亮
- 所有页面的"甲方"或"乙方" → 绿色高亮

### 示例2：技术文档章节标注

**需求**：为技术文档的不同章节使用不同颜色标注

```json
{
  "keywords": [
    {
      "text": "第一章",
      "color": "yellow",
      "pages": "1-10",
      "priority": 1
    },
    {
      "text": "第二章",
      "color": "blue",
      "pages": "11-20",
      "priority": 1
    },
    {
      "text": "重要",
      "color": "red",
      "pages": "1-5",
      "priority": 1
    },
    {
      "text": "重要",
      "color": "orange",
      "pages": "15-20",
      "priority": 1
    },
    {
      "text": "API接口",
      "color": "green",
      "context_phrase": "调用API接口",
      "priority": 1
    },
    {
      "text": "接口",
      "color": "purple",
      "priority": 2
    }
  ]
}
```

**效果**：
- 避免"API接口"和"接口"的重叠标记
- 同一关键词"重要"在不同章节使用不同颜色
- 精确的上下文匹配避免误标记

### 示例3：学术论文多层次标注

```json
{
  "keywords": [
    {
      "text": "深度学习算法",
      "color": "red",
      "pages": "3-8",
      "priority": 1
    },
    {
      "text": "深度学习",
      "color": "blue",
      "pages": "3-8",
      "priority": 2
    },
    {
      "text": "算法",
      "color": "green",
      "pages": "3-8",
      "priority": 3
    },
    {
      "text": "实验结果表明",
      "color": "orange",
      "context_phrase": "实验结果表明该方法",
      "pages": "10-15"
    },
    {
      "text": "结果",
      "color": "yellow",
      "pages": "10-15",
      "priority": 2
    }
  ]
}
```

**智能处理**：
- "深度学习算法"优先级最高，会被标记为红色
- "深度学习"和"算法"因与"深度学习算法"重叠被跳过
- "实验结果表明该方法"中的"结果"被精确标记为橙色
- 其他位置的"结果"标记为黄色

## 🏷️ 关键词ID功能

### 关键词唯一标识

每个关键词配置都有一个唯一的ID，用于标识和追踪处理结果：

```json
{
  "keywords": [
    {
      "id": "frontend_dev",
      "text": "前端开发",
      "color": "yellow",
      "pages": "1-5"
    },
    {
      "id": "backend_dev",
      "text": "后端开发",
      "color": "blue",
      "pages": "6-10"
    },
    {
      "id": "review_context_page3",
      "text": "审核",
      "color": "red",
      "context_phrase": "添加以供审核",
      "pages": "3"
    }
  ]
}
```

### ID生成规则

- **手动指定**：在配置中提供 `id` 字段
- **自动生成**：基于关键词内容的哈希值生成（格式：`kw_xxxxxxxx`）
- **唯一性保证**：相同内容的关键词会生成相同的ID，不同配置生成不同ID

### 编程API使用

```python
# 手动指定ID
id1 = highlighter.add_keyword("审核", color="red", id="custom_review")

# 自动生成ID
id2 = highlighter.add_keyword("提交", color="blue")

print(f"关键词ID: {id1}, {id2}")
```

### 处理结果按ID分组

```json
{
  "keywords_by_id": {
    "frontend_dev": {
      "id": "frontend_dev",
      "text": "前端开发",
      "matches_found": 5,
      "highlights_added": 5,
      "status": "success"
    },
    "review_context_page3": {
      "id": "review_context_page3",
      "text": "审核",
      "matches_found": 1,
      "highlights_added": 1,
      "status": "success"
    }
  },
  "summary": {
    "total_keywords": 8,
    "successful_keywords": 6,
    "failed_keywords": 2,
    "skipped_keywords": 0
  }
}
```

### ID功能优势

- 🏷️ **精确追踪**：每个关键词配置都有唯一标识
- 📊 **详细统计**：按ID分组显示处理结果
- 🔍 **问题定位**：快速识别哪些关键词成功/失败
- 🔄 **向后兼容**：现有配置自动生成ID，无需修改

## 🎯 上下文相似度匹配

### 智能模糊匹配

系统支持上下文短语的相似度匹配，解决PDF文本提取中的格式差异问题：

```json
{
  "global_options": {
    "context_similarity_threshold": 0.8
  },
  "keywords": [
    {
      "id": "review_keyword",
      "text": "审核",
      "context_phrase": "添加以供审核",
      "similarity_threshold": 0.9
    }
  ]
}
```

### 相似度阈值设置

- **全局设置**：`context_similarity_threshold` 在 `global_options` 中设置（默认1.0）
- **局部设置**：每个关键词可单独设置 `similarity_threshold`，覆盖全局设置
- **阈值范围**：0.0-1.0，1.0表示100%精确匹配

### 实际应用场景

#### 场景1：处理多余空格
```json
{
  "text": "审核",
  "context_phrase": "添加以供审核",
  "similarity_threshold": 0.8
}
```
**PDF中实际文本**：`"添加 以供 审核"`（多空格）
**匹配结果**：✅ 相似度0.8，成功匹配

#### 场景2：处理文字差异
```json
{
  "text": "提交",
  "context_phrase": "确认无误后提交",
  "similarity_threshold": 0.7
}
```
**PDF中实际文本**：`"请确认无误后提交"`（多字）
**匹配结果**：✅ 相似度0.7+，成功匹配

### 编程API使用

```python
# 全局相似度阈值
highlighter = MultiKeywordHighlighter('file.pdf')
highlighter.global_similarity_threshold = 0.8

# 局部相似度阈值
highlighter.add_keyword(
    "审核",
    color="red",
    context_phrase="添加以供审核",
    similarity_threshold=0.9
)
```

### 相似度算法

- **标准化处理**：自动处理多余空白字符
- **编辑距离**：使用SequenceMatcher计算相似度
- **智能分割**：将页面文本分割成合适的短语进行匹配

### 向后兼容性

- ✅ **默认精确匹配**：未设置阈值时使用1.0（100%匹配）
- ✅ **现有配置无需修改**：完全兼容现有配置文件
- ✅ **渐进式升级**：可选择性地为特定关键词启用模糊匹配

## 🎨 标记样式功能

### 多种标记样式支持

系统现在支持多种文本标记样式，不仅限于传统的高亮：

| 样式 | 说明 | 示例效果 |
|------|------|----------|
| `highlight` | 高亮标记（默认） | ![](examples/images/highlight.png) |
| `underline` | 下划线标记 | ![](examples/images/underline.png) |
| `strikeout` | 删除线标记 | ![](examples/images/strikeout.png) |
| `squiggly` | 波浪线标记 | ![](examples/images/squiggly.png) |

### 配置文件使用

```json
{
  "keywords": [
    {
      "text": "重要概念",
      "color": "yellow",
      "annotation_style": "highlight"
    },
    {
      "text": "需要确认",
      "color": "blue",
      "annotation_style": "underline"
    },
    {
      "text": "已过时",
      "color": "red",
      "annotation_style": "strikeout"
    },
    {
      "text": "不确定",
      "color": "orange",
      "annotation_style": "squiggly"
    }
  ]
}
```

### 编程API使用

```python
from pdf_highlighter import MultiKeywordHighlighter

highlighter = MultiKeywordHighlighter('document.pdf')

# 添加不同标记样式的关键词
highlighter.add_keyword("重要概念", color="yellow", annotation_style="highlight")
highlighter.add_keyword("需要确认", color="blue", annotation_style="underline")
highlighter.add_keyword("已过时", color="red", annotation_style="strikeout")
highlighter.add_keyword("不确定", color="orange", annotation_style="squiggly")

result = highlighter.process('output.pdf')
```

### 样式验证

系统会自动验证标记样式的有效性，支持的样式包括：
- `highlight` - 高亮（默认）
- `underline` - 下划线
- `strikeout` - 删除线
- `squiggly` - 波浪线

### 向后兼容性

- ✅ **默认样式**：未指定 `annotation_style` 时自动使用 `highlight`
- ✅ **现有配置无需修改**：完全兼容现有配置文件
- ✅ **渐进式升级**：可选择性地为特定关键词启用不同样式

## 📊 API调用和返回结果

### 编程API使用

```python
from pdf_highlighter import MultiKeywordHighlighter

# 创建高亮器
highlighter = MultiKeywordHighlighter('document.pdf')

# 添加关键词（返回关键词ID）
id1 = highlighter.add_keyword("审核", color="red", id="custom_review")
id2 = highlighter.add_keyword("提交", color="blue")  # 自动生成ID

# 或从配置文件加载
highlighter.load_config_file('config.json')

# 执行处理
result = highlighter.process('output.pdf')

# 检查处理结果
if result['success']:
    print(f"成功添加 {result['total_highlights']} 个高亮")

    # 访问详细结果
    for keyword_id, stats in result['keywords_by_id'].items():
        print(f"[{keyword_id}] {stats['text']}: {stats['highlights_added']} 个高亮")
else:
    print(f"处理失败: {result['error']}")
```

### 返回结果格式

处理完成后返回简洁的结果字典：

```json
{
  "success": true,
  "input_file": "document.pdf",
  "output_file": "document_highlighted.pdf",
  "total_highlights": 15,

  "keywords_by_id": {
    "custom_review": {
      "id": "custom_review",
      "text": "审核",
      "matches_found": 5,
      "highlights_added": 5,
      "status": "success",
      "color": [1, 0, 0],
      "opacity": 0.8,
      "pages": "1-5",
      "context_phrase": "提交审核",
      "priority": 1,
      "case_sensitive": false,
      "regex": false,
      "whole_word": false,
      "annotation_style": "highlight"
    },
    "not_found_keyword": {
      "id": "not_found_keyword",
      "text": "不存在的关键词",
      "matches_found": 0,
      "highlights_added": 0,
      "status": "no_matches",
      "color": [0, 1, 0],
      "opacity": 0.6,
      "annotation_style": "underline"
    }
  },

  "keywords_by_status": {
    "successful": {
      "custom_review": {
        "id": "custom_review",
        "text": "审核",
        "highlights_added": 5,
        "status": "success"
      }
    },
    "failed": {
      "not_found_keyword": {
        "id": "not_found_keyword",
        "text": "不存在的关键词",
        "highlights_added": 0,
        "status": "no_matches"
      }
    }
  },

  "summary": {
    "total_keywords": 8,
    "successful_keywords": 6,
    "failed_keywords": 2,
    "skipped_keywords": 0
  },

  "keywords_processed": {
    "审核": { /* 按文本分组，向后兼容 */ }
  }
}
```

### 结果字段说明

#### 主要字段
- `success`: 处理是否成功
- `total_highlights`: 总共添加的高亮数量
- `keywords_by_id`: 按关键词ID分组的完整结果
- `keywords_by_status`: 按匹配状态归类的关键词
- `summary`: 统计摘要

#### 关键词信息 (`keywords_by_id[id]`)
- `id`: 关键词唯一标识符
- `text`: 关键词文本
- `matches_found`: 找到的匹配数量
- `highlights_added`: 成功添加的高亮数量
- `status`: 处理状态
  - `success`: 成功高亮
  - `no_matches`: 未找到匹配
  - `failed`: 处理失败
  - `skipped`: 被跳过
- 配置信息：`color`、`opacity`、`pages`、`context_phrase`、`priority` 等
- `annotation_style`: 标记样式

#### 关键词状态归类 (`keywords_by_status`)
- `successful`: 成功高亮的关键词（`highlights_added > 0`）
- `failed`: 未被高亮的关键词（`highlights_added = 0`）

每个归类中的关键词包含相同的字段结构，便于快速访问特定状态的关键词。

### 使用场景示例

#### 1. 使用归类结果快速检查
```python
result = highlighter.process()

# 使用新的归类功能
keywords_by_status = result['keywords_by_status']

# 查看成功高亮的关键词
successful = keywords_by_status['successful']
print("✅ 成功高亮的关键词:")
for keyword_id, stats in successful.items():
    print(f"  [{keyword_id}] '{stats['text']}' -> {stats['highlights_added']} 个高亮")

# 查看未被高亮的关键词
failed = keywords_by_status['failed']
print("❌ 未被高亮的关键词:")
for keyword_id, stats in failed.items():
    print(f"  [{keyword_id}] '{stats['text']}' -> {stats['status']}")

print(f"\n总结: {len(successful)} 个成功，{len(failed)} 个失败")
```

#### 2. 简单的成功/失败统计
```python
# 使用统计摘要
summary = result['summary']
print(f"总关键词: {summary['total_keywords']}")
print(f"成功高亮: {summary['successful_keywords']}")
print(f"未被高亮: {summary['failed_keywords']}")
print(f"总高亮数: {result['total_highlights']}")
```

#### 3. 按ID快速查找关键词状态
```python
# 方式1：在归类结果中查找
keyword_id = "custom_review"
keywords_by_status = result['keywords_by_status']

if keyword_id in keywords_by_status['successful']:
    print(f"关键词 [{keyword_id}] 成功高亮")
elif keyword_id in keywords_by_status['failed']:
    print(f"关键词 [{keyword_id}] 未被高亮")

# 方式2：获取特定状态的所有关键词文本
successful_texts = [stats['text'] for stats in keywords_by_status['successful'].values()]
failed_texts = [stats['text'] for stats in keywords_by_status['failed'].values()]

print(f"成功的关键词: {successful_texts}")
print(f"失败的关键词: {failed_texts}")
```

#### 4. 计算成功率
```python
# 快速计算成功率
keywords_by_status = result['keywords_by_status']
total = len(result['keywords_by_id'])
successful_count = len(keywords_by_status['successful'])
success_rate = successful_count / total * 100

print(f"成功率: {successful_count}/{total} = {success_rate:.1f}%")
```

## 📊 功能对比表

| 功能 | 基础模式 | 页面级配置 | 上下文匹配 | 相似度匹配 | 重叠处理 | ID功能 | 标记样式 | 组合使用 |
|------|----------|------------|------------|------------|----------|--------|----------|----------|
| **单关键词全文高亮** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **指定页面范围** | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **精确上下文匹配** | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **模糊上下文匹配** | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **避免重叠标记** | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ |
| **同词不同色** | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **优先级控制** | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ |
| **关键词ID追踪** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |
| **标记样式选择** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| **状态归类统计** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |

### 使用建议

| 场景 | 推荐功能 | 配置示例 |
|------|----------|----------|
| **简单高亮** | 基础模式 | `pdf-highlight doc.pdf "关键词"` |
| **章节分类** | 页面级配置 | `{"text": "第一章", "pages": "1-10"}` |
| **精确匹配** | 上下文匹配 | `{"text": "审核", "context_phrase": "添加以供审核"}` |
| **模糊匹配** | 相似度匹配 | `{"text": "审核", "context_phrase": "添加以供审核", "similarity_threshold": 0.8}` |
| **结果追踪** | ID功能 | `{"id": "custom_id", "text": "关键词"}` |
| **样式标记** | 标记样式 | `{"text": "关键词", "annotation_style": "underline"}` |
| **复杂文档** | 组合使用 | 完整配置文件 |

## 🎯 最佳实践

### 1. 配置文件组织
```json
{
  "keywords": [
    // 高优先级关键词（长词、重要词）
    {
      "text": "长关键词或重要概念",
      "priority": 1,
      "color": "red"
    },
    // 中优先级关键词
    {
      "text": "中等关键词",
      "priority": 5,
      "color": "blue"
    },
    // 低优先级关键词（短词、通用词）
    {
      "text": "短词",
      "priority": 10,
      "color": "yellow"
    }
  ]
}
```

### 2. 优先级设置建议
- **1-3**：重要的长关键词、专业术语
- **4-6**：中等重要的关键词
- **7-10**：通用词汇、短关键词

### 3. 颜色搭配建议
- **红色**：重要、警告、关键信息
- **蓝色**：技术术语、专业概念
- **绿色**：正面信息、确认操作
- **黄色**：一般信息、提醒
- **橙色**：注意事项、中等重要

## 🛡️ 错误处理和健壮性

### 自动错误处理机制

系统具备完善的错误处理和降级策略，确保配置错误不会导致程序中断：

#### 1. 上下文配置错误处理

当 `context_phrase` 不包含指定的 `text` 时：

```json
{
  "text": "审核",
  "context_phrase": "添加以供1审2核"  // 错误：关键字被数字分隔
}
```

**系统处理**：
- ⚠️ **记录警告日志**：详细说明配置问题
- 🔄 **降级处理**：在搜索时进行降级处理
- ✅ **继续处理**：根据降级策略处理关键字
- 🚫 **不会中断**：程序继续正常运行

#### 2. 支持的错误情况

| 错误类型 | 示例 | 处理方式 |
|----------|------|----------|
| **关键字被分隔** | `"审核"` 在 `"添加以供1审2核"` | 根据降级策略处理 |
| **关键字不存在** | `"删除"` 在 `"保存文件操作"` | 根据降级策略处理 |
| **上下文短语不存在** | PDF中找不到上下文短语 | 根据降级策略处理 |
| **空上下文短语** | `"context_phrase": ""` | 忽略上下文配置 |
| **null上下文** | `"context_phrase": null` | 正常处理 |

#### 3. 日志信息示例

```
WARNING - 关键词 '审核' 不在上下文短语 '添加以供1审2核' 中，将在搜索时进行降级处理
WARNING - 未找到上下文短语 '这个短语不存在'，降级为普通关键字搜索
INFO - ✅ [keyword_id] '审核': 添加 5 个高亮
WARNING - ❌ [keyword_id] '不存在关键词': 未被高亮
```

#### 4. 降级策略

```json
{
  "keywords": [
    {
      "text": "审核",
      "color": "red",
      "context_phrase": "添加以供1审2核"  // 配置错误
    }
  ]
}
```

**处理结果**：
```json
{
  "keywords_by_status": {
    "successful": {
      "keyword_id": {
        "id": "keyword_id",
        "text": "审核",
        "highlights_added": 5,
        "status": "success"
      }
    },
    "failed": {}
  }
}
```

### 健壮性保证

- ✅ **配置容错**：无效配置不会导致程序崩溃
- ✅ **自动修复**：系统自动修正可修复的配置错误
- ✅ **详细日志**：提供清晰的错误信息和处理建议
- ✅ **优雅降级**：在保证功能可用的前提下处理错误
- ✅ **向后兼容**：错误处理不影响正常配置的使用

#### 5. 上下文短语不存在的处理策略

当配置的上下文短语在PDF文件中不存在时，系统提供三种处理策略：

**策略配置**：
```json
{
  "global_options": {
    "context_fallback_strategy": "fallback_to_normal"
  }
}
```

或编程API：
```python
highlighter = MultiKeywordHighlighter('file.pdf', context_fallback_strategy='skip')
```

**可选策略**：

| 策略 | 说明 | 适用场景 | 结果 |
|------|------|----------|------|
| `fallback_to_normal` | 降级为普通关键字搜索（默认） | 希望在配置错误时仍能获得结果 | 忽略上下文，按普通模式搜索 |
| `skip` | 完全跳过该关键字 | 只处理配置正确的关键字 | 不处理该关键字 |
| `warn_only` | 仅记录警告，不搜索 | 严格模式，只处理完全正确的配置 | 记录警告，不处理 |

**示例效果**：
```json
{
  "text": "审核",
  "context_phrase": "这个短语在PDF中不存在"
}
```

- `fallback_to_normal`: 在整个PDF中搜索"审核"
- `skip`: 跳过"审核"关键字，不进行任何处理
- `warn_only`: 记录警告，"审核"不会被高亮

### 场景2：合同文档重点标记

#### 配置文件方式
```json
{
  "keywords": [
    {"text": "甲方|乙方", "color": "red", "regex": true},
    {"text": "违约责任", "color": "yellow", "opacity": 0.8},
    {"text": "终止|解除", "color": "orange", "regex": true},
    {"text": "保密条款", "color": "purple", "opacity": 0.7}
  ]
}
```

```bash
pdf-highlight contract.pdf --config contract_config.json
```

### 场景3：技术文档代码高亮

#### 多关键词模式
```bash
# 一次性高亮所有技术关键词
pdf-highlight manual.pdf --keywords "function" "class" "API" "import" \
  --colors purple blue cyan green --regex
```

#### 配置文件模式
```json
{
  "keywords": [
    {"text": "function|class|method", "color": "purple", "regex": true},
    {"text": "API", "color": "cyan", "whole_word": true, "case_sensitive": true},
    {"text": "import|export", "color": "green", "regex": true},
    {"text": "TODO|FIXME|BUG", "color": "red", "regex": true}
  ]
}
```

### 场景4：章节分类标注

#### 页面级章节关键词配置
```json
{
  "keywords": [
    {
      "text": "第一章|Chapter 1",
      "color": "yellow",
      "regex": true,
      "pages": "1-10"
    },
    {
      "text": "第二章|Chapter 2",
      "color": "blue",
      "regex": true,
      "pages": "11-20"
    },
    {
      "text": "第三章|Chapter 3",
      "color": "green",
      "regex": true,
      "pages": "21-30"
    },
    {
      "text": "重要",
      "color": "red",
      "opacity": 0.8,
      "pages": "1-5"
    },
    {
      "text": "重要",
      "color": "orange",
      "opacity": 0.8,
      "pages": "15-20"
    },
    {
      "text": "重要",
      "color": "purple",
      "opacity": 0.8,
      "pages": "25-30"
    }
  ]
}
```

```bash
pdf-highlight textbook.pdf --config chapter_config.json
```

### 场景5：扫描文档OCR识别

#### 基本OCR使用
```bash
# 搜索扫描PDF中的文字
pdf-highlight scanned.pdf "重要信息" --enable-ocr

# 仅搜索图片中的文字
pdf-highlight document.pdf "图表标题" --ocr-only

# 指定中文识别
pdf-highlight chinese_doc.pdf "关键词" --enable-ocr --ocr-lang chi_sim
```

#### Python OCR API
```python
from pdf_highlighter import PDFHighlighter
from pdf_highlighter.config.settings import set_config

# 配置OCR参数
set_config('ocr.language', 'chi_sim+eng')
set_config('ocr.confidence_threshold', 70)

with PDFHighlighter('scanned.pdf', enable_ocr=True) as highlighter:
    # 搜索图片中的中文文字
    results = highlighter.search_keywords('产品说明', enable_ocr=True)

    # 仅搜索图片内容
    image_results = highlighter.search_in_images_only('图表数据')

    # 使用不同颜色高亮
    highlighter.add_highlights(results, color=(1, 1, 0))  # 黄色
    highlighter.add_highlights(image_results, color=(0, 1, 0))  # 绿色

    highlighter.save_pdf('ocr_highlighted.pdf')
```

## 🔧 高级功能

### 多关键词批处理

#### 使用配置文件批处理
```bash
#!/bin/bash
# 批量处理多个PDF文件，使用统一的多关键词配置

config_file="multi_keywords.json"

for file in *.pdf; do
    echo "处理文件: $file"
    pdf-highlight "$file" --config "$config_file" -o "highlighted_$file"
done
```

#### Python多关键词批处理
```python
import os
from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
logger = get_logger()

def batch_multi_highlight(directory, config_file):
    """批量多关键词高亮目录中的PDF文件"""
    for filename in os.listdir(directory):
        if filename.endswith('.pdf'):
            input_path = os.path.join(directory, filename)
            output_path = os.path.join(directory, f'highlighted_{filename}')

            try:
                # 使用多关键词高亮器
                highlighter = MultiKeywordHighlighter(input_path)
                highlighter.load_config_file(config_file)

                result = highlighter.process(output_path)

                if result['success']:
                    logger.info(f"处理完成: {filename}, 高亮数: {result['total_highlights']}")
                else:
                    logger.error(f"处理失败 {filename}: {result['error']}")

            except Exception as e:
                logger.error(f"处理失败 {filename}: {e}")

# 使用示例
batch_multi_highlight('/path/to/pdfs', 'multi_config.json')
```

### 编程式多关键词配置

```python
from pdf_highlighter import MultiKeywordHighlighter, KeywordConfig

def create_academic_highlighter(pdf_path):
    """创建学术论文专用的多关键词高亮器"""
    highlighter = MultiKeywordHighlighter(pdf_path)

    # 添加学术关键词
    academic_keywords = [
        KeywordConfig('深度学习', color='blue', opacity=0.6),
        KeywordConfig('神经网络', color='green', opacity=0.6),
        KeywordConfig('机器学习', color='orange', opacity=0.6),
        KeywordConfig(r'算法|模型', color='purple', opacity=0.7, regex=True),
        KeywordConfig('数据集', color='red', opacity=0.5)
    ]

    highlighter.keyword_configs = academic_keywords
    return highlighter

# 使用示例
highlighter = create_academic_highlighter('paper.pdf')
result = highlighter.process('highlighted_paper.pdf')
print(f"学术论文处理完成，添加了 {result['total_highlights']} 个高亮")
```

## 🛠️ 开发

### 环境设置

```bash
# 克隆仓库
git clone https://github.com/jiwei/pdf-highlighter.git
cd pdf-highlighter

# 安装开发依赖
uv sync --extra dev

# 运行测试
uv run pytest

# 代码格式化
uv run black src/
uv run isort src/

# 类型检查
uv run mypy src/
```

### 项目结构

```
pdf-highlighter/
├── src/pdf_highlighter/          # 主要源码
│   ├── cli/                      # 命令行接口
│   ├── core/                     # 核心功能
│   ├── utils/                    # 工具模块
│   └── config/                   # 配置管理
├── tests/                        # 测试文件
├── examples/                     # 使用示例
├── docs/                         # 文档
└── pyproject.toml               # 项目配置
```

## ❓ 常见问题

### Q: 支持哪些PDF格式？
A: 支持所有标准PDF格式，包括由Word、Excel、PowerPoint、图片转换等生成的PDF文件。

### Q: 如何同时高亮多个关键词？
A: 有三种方式：
1. **配置文件模式**（推荐）：`pdf-highlight input.pdf --config config.json`
2. **命令行模式**：`pdf-highlight input.pdf --keywords "词1" "词2" --colors yellow blue`
3. **编程API**：使用`MultiKeywordHighlighter`类

### Q: 多关键词模式支持哪些功能？
A: 每个关键词可以独立设置：
- 不同的颜色和透明度
- 正则表达式搜索
- 大小写敏感
- 完整单词匹配
- 匹配项数量限制
- 不同的标记样式（高亮、下划线、删除线、波浪线）

### Q: 配置文件格式是什么？
A: 使用JSON格式，可通过`pdf-highlight --create-config sample.json`创建模板。

### Q: 高亮后的PDF文件大小会增加很多吗？
A: 高亮注释只是添加元数据，文件大小增加很少，通常不超过原文件的5%。

### Q: 支持加密的PDF文件吗？
A: 目前不支持加密的PDF文件，需要先解密后再处理。

### Q: 如何处理大型PDF文件？
A: 工具支持懒加载和内存优化，可以处理较大的PDF文件。如遇到内存问题，可以分页处理。

### Q: 多关键词模式的性能如何？
A: 多关键词模式在一次执行中处理所有关键词，比多次执行单关键词更高效，减少了文件读写次数。

### Q: OCR功能支持哪些语言？
A: 支持Tesseract OCR支持的所有语言，包括中文简体(chi_sim)、中文繁体(chi_tra)、英文(eng)、日文(jpn)、韩文(kor)等。可以组合使用，如`chi_sim+eng`。

### Q: 如何提高OCR识别精度？
A: 可以通过以下方式提高精度：
1. 调整图片预处理参数（放大倍数、去噪、二值化）
2. 设置合适的置信度阈值
3. 选择正确的语言包
4. 确保原始图片质量良好

### Q: OCR功能需要安装额外软件吗？
A: 是的，需要安装：
1. Python依赖：`pip install pdf-highlighter[ocr]`
2. Tesseract OCR引擎
3. 对应语言的训练数据包

### Q: 为什么OCR识别速度较慢？
A: OCR处理比文本搜索复杂，涉及图像处理和文字识别。可以通过启用并行处理、缓存和合理的预处理参数来优化性能。

## 🐛 故障排除

### 常见错误及解决方案

1. **"PDF文件不存在"**
   - 检查文件路径是否正确
   - 确保文件确实存在且可读

2. **"无效的正则表达式"**
   - 检查正则表达式语法
   - 使用在线正则表达式测试工具验证

3. **"页码超出范围"**
   - 检查PDF文件的实际页数
   - 确保页码范围格式正确

4. **"没有找到匹配项"**
   - 检查关键词拼写
   - 尝试不区分大小写搜索
   - 检查PDF是否为扫描版（图片）

5. **"配置文件格式错误"**
   - 使用`--create-config`创建标准模板
   - 检查JSON语法是否正确
   - 确保所有字符串都用双引号包围

6. **"颜色数量与关键词数量不匹配"**
   - 确保`--colors`参数数量与`--keywords`参数数量一致
   - 或者省略`--colors`使用默认颜色

7. **"关键词配置缺少必需字段"**
   - 确保配置文件中每个关键词都有`text`字段
   - 参考示例配置文件格式

8. **"多关键词处理失败"**
   - 检查每个关键词的配置是否正确
   - 使用`--dry-run`预览配置
   - 查看详细错误信息（`-v`参数）

9. **"OCR功能不可用"**
   - 确保已安装OCR依赖：`pip install pdf-highlighter[ocr]`
   - 检查Tesseract OCR引擎是否正确安装
   - 验证所需语言包是否已安装

10. **"TesseractNotFoundError"**
    - 确保Tesseract可执行文件在系统PATH中
    - Windows用户可能需要手动设置tesseract路径
    - 检查Tesseract安装是否完整

11. **"OCR识别结果不准确"**
    - 调整OCR置信度阈值（降低阈值包含更多结果）
    - 检查图片质量和分辨率
    - 尝试不同的图片预处理参数
    - 确认使用了正确的语言包

12. **"OCR处理速度慢"**
    - 启用并行处理和缓存功能
    - 适当调整图片预处理参数
    - 考虑只在必要时使用OCR功能

## 📋 快速参考

### 命令行快速参考
```bash
# 单关键词
pdf-highlight input.pdf "关键词" -o output.pdf

# 多关键词（命令行）
pdf-highlight input.pdf --keywords "前端" "后端" --colors yellow blue

# 多关键词（配置文件）
pdf-highlight input.pdf --config config.json

# 创建配置文件模板
pdf-highlight --create-config sample.json

# 预览模式
pdf-highlight input.pdf --config config.json --dry-run

# 页码范围
pdf-highlight input.pdf "关键词" -p 1,3-5,7

# 正则表达式
pdf-highlight input.pdf "前端|后端" --regex

# 不同标记样式
pdf-highlight input.pdf --keywords "重要" "待确认" "已过时" \
  --colors yellow blue red --annotation-styles highlight underline strikeout
```

### Python API快速参考
```python
# 多关键词高亮（推荐）
from pdf_highlighter import MultiKeywordHighlighter

# 创建高亮器
highlighter = MultiKeywordHighlighter('input.pdf')

# 添加关键词
id1 = highlighter.add_keyword('前端', color='yellow', id='frontend')
id2 = highlighter.add_keyword('后端', color='blue', id='backend')

# 执行处理
result = highlighter.process('output.pdf')

# 检查结果
if result['success']:
    # 查看成功/失败的关键词
    successful = result['keywords_by_status']['successful']
    failed = result['keywords_by_status']['failed']

    print(f"成功: {[stats['text'] for stats in successful.values()]}")
    print(f"失败: {[stats['text'] for stats in failed.values()]}")
else:
    print(f"处理失败: {result['error']}")

# 从配置文件加载
highlighter.load_config_file('config.json')
result = highlighter.process()
```

## 📞 支持

- 📧 邮箱：<EMAIL>
- 🐛 问题报告：[GitHub Issues](https://github.com/jiwei/pdf-highlighter/issues)
- 📖 文档：[项目文档](https://github.com/jiwei/pdf-highlighter#readme)

## 🙏 致谢

- [PyMuPDF](https://pymupdf.readthedocs.io/) - 强大的PDF处理库
- [uv](https://github.com/astral-sh/uv) - 现代Python包管理器
- 所有贡献者和用户的支持

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！