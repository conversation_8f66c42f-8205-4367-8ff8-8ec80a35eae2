#!/usr/bin/env python3
"""
验证README文档中的示例是否与实际实现匹配
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_readme_api_examples():
    """测试README中的API示例"""
    print("=== 验证README中的API示例 ===\n")
    
    try:
        # 测试导入
        from pdf_highlighter import MultiKeywordHighlighter
        print("✅ 导入成功: from pdf_highlighter import MultiKeywordHighlighter")
        
        # 找到测试PDF文件
        test_files = list(project_root.glob("*.pdf"))
        if not test_files:
            print("❌ 未找到测试PDF文件")
            return False
        
        test_file = test_files[0]
        print(f"✅ 找到测试文件: {test_file}")
        
        # 测试README中的基本API示例
        print("\n--- 测试基本API示例 ---")
        
        # 创建高亮器
        highlighter = MultiKeywordHighlighter(str(test_file))
        print("✅ 创建高亮器成功")
        
        # 添加关键词（返回ID）
        id1 = highlighter.add_keyword('审核', color='yellow', id='frontend')
        id2 = highlighter.add_keyword('应用', color='blue', id='backend')
        print(f"✅ 添加关键词成功，返回ID: {id1}, {id2}")
        
        # 执行处理
        result = highlighter.process()
        print("✅ 执行处理成功")
        
        # 检查结果结构
        required_fields = ['success', 'keywords_by_status', 'keywords_by_id', 'summary']
        missing_fields = [field for field in required_fields if field not in result]
        
        if not missing_fields:
            print("✅ 返回结果包含所有必需字段")
        else:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        
        # 检查 keywords_by_status 结构
        if 'keywords_by_status' in result:
            keywords_by_status = result['keywords_by_status']
            
            if 'successful' in keywords_by_status and 'failed' in keywords_by_status:
                print("✅ keywords_by_status 结构正确")
                
                # 测试README中的示例代码
                successful = keywords_by_status['successful']
                failed = keywords_by_status['failed']
                
                successful_texts = [stats['text'] for stats in successful.values()]
                failed_texts = [stats['text'] for stats in failed.values()]
                
                print(f"✅ 成功关键词: {successful_texts}")
                print(f"✅ 失败关键词: {failed_texts}")
                
            else:
                print("❌ keywords_by_status 结构不正确")
                return False
        else:
            print("❌ 缺少 keywords_by_status 字段")
            return False
        
        # 测试配置文件加载
        print("\n--- 测试配置文件加载 ---")
        
        # 创建测试配置文件
        test_config = {
            "keywords": [
                {
                    "id": "test_review",
                    "text": "审核",
                    "color": "red",
                    "opacity": 0.8
                },
                {
                    "id": "test_app",
                    "text": "应用",
                    "color": "green",
                    "context_phrase": "苹果ipa应用"
                }
            ]
        }
        
        config_file = project_root / "examples" / "readme_test_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, indent=2, ensure_ascii=False)
        
        # 测试从配置文件加载
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        highlighter2.load_config_file(str(config_file))
        print("✅ 配置文件加载成功")
        
        result2 = highlighter2.process()
        if result2['success']:
            print("✅ 配置文件处理成功")
        else:
            print(f"❌ 配置文件处理失败: {result2['error']}")
            return False
        
        print("\n✅ 所有README示例验证通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_result_structure_consistency():
    """测试返回结果结构的一致性"""
    print("\n=== 验证返回结果结构一致性 ===\n")
    
    try:
        from pdf_highlighter import MultiKeywordHighlighter
        
        test_files = list(project_root.glob("*.pdf"))
        if not test_files:
            return False
        
        test_file = test_files[0]
        
        # 测试不同的添加方式是否返回一致的结构
        highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 添加存在和不存在的关键词
        highlighter.add_keyword("审核", color="red", id="existing")
        highlighter.add_keyword("不存在的关键词", color="blue", id="not_found")
        
        result = highlighter.process()
        
        # 验证结构一致性
        print("验证返回结果结构:")
        
        # 检查主要字段
        main_fields = ['success', 'total_highlights', 'keywords_by_id', 'keywords_by_status', 'summary']
        for field in main_fields:
            if field in result:
                print(f"✅ {field}: 存在")
            else:
                print(f"❌ {field}: 缺失")
                return False
        
        # 检查 keywords_by_status 的结构
        keywords_by_status = result['keywords_by_status']
        status_fields = ['successful', 'failed']
        for field in status_fields:
            if field in keywords_by_status:
                print(f"✅ keywords_by_status.{field}: 存在")
            else:
                print(f"❌ keywords_by_status.{field}: 缺失")
                return False
        
        # 验证归类的正确性
        all_keywords = set(result['keywords_by_id'].keys())
        successful_keywords = set(keywords_by_status['successful'].keys())
        failed_keywords = set(keywords_by_status['failed'].keys())
        classified_keywords = successful_keywords | failed_keywords
        
        if all_keywords == classified_keywords:
            print("✅ 关键词归类完整")
        else:
            print(f"❌ 关键词归类不完整")
            print(f"   所有关键词: {all_keywords}")
            print(f"   已归类关键词: {classified_keywords}")
            return False
        
        # 验证归类的准确性
        for keyword_id in successful_keywords:
            stats = result['keywords_by_id'][keyword_id]
            if stats['highlights_added'] <= 0:
                print(f"❌ 归类错误: {keyword_id} 在成功组但没有高亮")
                return False
        
        for keyword_id in failed_keywords:
            stats = result['keywords_by_id'][keyword_id]
            if stats['highlights_added'] > 0:
                print(f"❌ 归类错误: {keyword_id} 在失败组但有高亮")
                return False
        
        print("✅ 关键词归类准确性验证通过")
        
        print("\n✅ 返回结果结构一致性验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 结构一致性验证失败: {e}")
        return False


if __name__ == "__main__":
    success1 = test_readme_api_examples()
    success2 = test_result_structure_consistency()
    
    print("\n" + "="*60)
    if success1 and success2:
        print("🎉 所有验证通过！README文档与实际实现完全匹配")
    else:
        print("❌ 验证失败，存在不匹配的内容")
    print("="*60)
