"""
命令行参数解析模块

使用argparse定义和解析所有命令行参数。
"""

import argparse
import sys


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器
    
    Returns:
        argparse.ArgumentParser: 配置好的参数解析器
    """
    parser = argparse.ArgumentParser(
        prog='pdf-highlight',
        description='PDF关键词高亮工具 - 基于PyMuPDF的PDF文件关键词搜索和高亮注释工具',
        epilog='''
使用示例:
  %(prog)s input.pdf "关键词"                    # 高亮所有"关键词"
  %(prog)s input.pdf "前端" -o output.pdf        # 指定输出文件
  %(prog)s input.pdf "工程师" -p 1,3-5           # 只在第1页和第3-5页搜索
  %(prog)s input.pdf "Python" -n 2              # 只高亮第2个匹配项
  %(prog)s input.pdf "test" -c red -a 0.8       # 使用红色高亮，透明度0.8
  %(prog)s input.pdf "前端|后端" --regex         # 使用正则表达式搜索
  %(prog)s input.pdf "API" --case-sensitive     # 区分大小写搜索
  %(prog)s input.pdf "word" --whole-word        # 完整单词匹配
  %(prog)s input.pdf "文字" --enable-ocr        # 启用OCR，搜索图片中的文字
  %(prog)s input.pdf "图片文字" --ocr-only      # 仅搜索图片中的文字
  %(prog)s input.pdf "中文" --enable-ocr --ocr-lang chi_sim  # 指定OCR语言
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 必需参数
    parser.add_argument(
        'input_file',
        nargs='?',
        help='输入PDF文件路径（当使用--create-config时可选）'
    )
    
    parser.add_argument(
        'keyword',
        nargs='?',
        help='要搜索和高亮的关键词（当使用--config时可选）'
    )
    
    # 可选参数
    parser.add_argument(
        '-o', '--output',
        dest='output_file',
        help='输出PDF文件路径（默认：输入文件名_highlighted.pdf）'
    )
    
    parser.add_argument(
        '-p', '--pages',
        help='指定搜索的页码范围，格式：1,3-5,7（默认：所有页面）'
    )
    
    parser.add_argument(
        '-n', '--occurrence',
        type=int,
        help='指定高亮第几个匹配项（默认：所有匹配项）'
    )
    
    parser.add_argument(
        '-c', '--color',
        default='yellow',
        help='高亮颜色，支持颜色名称(yellow/red/green/blue等)、十六进制(#FF0000)或RGB值(255,0,0)（默认：yellow）'
    )
    
    parser.add_argument(
        '-a', '--opacity',
        type=float,
        default=0.5,
        help='高亮透明度，范围0-1（默认：0.5）'
    )
    
    parser.add_argument(
        '--case-sensitive',
        action='store_true',
        help='区分大小写搜索（默认：不区分）'
    )
    
    parser.add_argument(
        '--regex',
        action='store_true',
        help='启用正则表达式搜索模式'
    )
    
    parser.add_argument(
        '--whole-word',
        action='store_true',
        help='只匹配完整单词'
    )

    parser.add_argument(
        '--enable-ocr',
        action='store_true',
        help='启用OCR功能，搜索PDF图片中的文字'
    )

    parser.add_argument(
        '--ocr-lang',
        default='chi_sim+eng',
        help='OCR识别语言设置（默认：chi_sim+eng，支持中文简体+英文）'
    )

    parser.add_argument(
        '--ocr-confidence',
        type=int,
        default=60,
        help='OCR识别置信度阈值，范围0-100（默认：60）'
    )

    parser.add_argument(
        '--ocr-only',
        action='store_true',
        help='仅搜索图片中的文字，不搜索PDF文本内容'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='预览模式，只显示搜索结果不生成文件'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细输出信息'
    )
    
    parser.add_argument(
        '--config',
        help='多关键词配置文件路径（JSON格式）'
    )

    parser.add_argument(
        '--keywords',
        nargs='+',
        help='多个关键词列表（与--colors配合使用）'
    )

    parser.add_argument(
        '--colors',
        nargs='+',
        help='对应关键词的颜色列表'
    )

    parser.add_argument(
        '--create-config',
        help='创建示例配置文件到指定路径'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='%(prog)s 0.1.0'
    )
    
    return parser


def parse_args(args=None) -> argparse.Namespace:
    """解析命令行参数
    
    Args:
        args: 命令行参数列表，None表示使用sys.argv
        
    Returns:
        Namespace: 解析后的参数对象
    """
    parser = create_parser()
    
    # 如果没有提供参数，显示帮助信息
    if args is None:
        args = sys.argv[1:]
    
    if not args:
        parser.print_help()
        sys.exit(1)
    
    return parser.parse_args(args)


def validate_parsed_args(args: argparse.Namespace) -> argparse.Namespace:
    """验证解析后的参数

    Args:
        args: 解析后的参数对象

    Returns:
        Namespace: 验证后的参数对象

    Raises:
        SystemExit: 参数验证失败时退出程序
    """
    from pdf_highlighter.core.validator import ParameterValidator

    try:
        # 创建配置文件模式不需要验证输入文件
        if hasattr(args, 'create_config') and args.create_config:
            return args

        # 验证PDF文件路径
        if not args.input_file:
            raise ValueError("必须提供输入PDF文件路径")
        args.input_file = ParameterValidator.validate_pdf_path(args.input_file)

        # 处理多关键词模式
        if args.config:
            # 配置文件模式：关键词可选
            if args.keyword:
                args.keyword = ParameterValidator.validate_keyword(args.keyword)
        elif args.keywords:
            # 多关键词命令行模式
            validated_keywords = []
            for keyword in args.keywords:
                validated_keywords.append(ParameterValidator.validate_keyword(keyword))
            args.keywords = validated_keywords

            # 验证颜色列表
            if args.colors:
                if len(args.colors) != len(args.keywords):
                    raise ValueError(f"颜色数量({len(args.colors)})与关键词数量({len(args.keywords)})不匹配")
                validated_colors = []
                for color in args.colors:
                    validated_colors.append(ParameterValidator.validate_color(color))
                args.colors = validated_colors
        else:
            # 单关键词模式：关键词必需
            if not args.keyword:
                raise ValueError("必须提供关键词、--keywords参数或--config配置文件")
            args.keyword = ParameterValidator.validate_keyword(args.keyword)

        # 验证单关键词模式的颜色和透明度
        if hasattr(args, 'color') and args.color:
            args.color = ParameterValidator.validate_color(args.color)

        if hasattr(args, 'opacity') and args.opacity:
            args.opacity = ParameterValidator.validate_opacity(args.opacity)

        return args

    except (FileNotFoundError, ValueError, PermissionError) as e:
        print(f"参数错误: {e}", file=sys.stderr)
        sys.exit(1)


def print_args_summary(args: argparse.Namespace) -> None:
    """打印参数摘要
    
    Args:
        args: 参数对象
    """
    print("=== 参数设置 ===")
    print(f"输入文件: {args.input_file}")
    print(f"搜索关键词: '{args.keyword}'")
    
    if hasattr(args, 'output_file') and args.output_file:
        print(f"输出文件: {args.output_file}")
    else:
        print("输出文件: 自动生成")
    
    if args.pages:
        print(f"页码范围: {args.pages}")
    else:
        print("页码范围: 所有页面")
    
    if args.occurrence:
        print(f"匹配项序号: 第{args.occurrence}个")
    else:
        print("匹配项序号: 所有匹配项")
    
    print(f"高亮颜色: {args.color}")
    print(f"透明度: {args.opacity}")
    
    options = []
    if args.case_sensitive:
        options.append("区分大小写")
    if args.regex:
        options.append("正则表达式")
    if args.whole_word:
        options.append("完整单词")
    if hasattr(args, 'enable_ocr') and args.enable_ocr:
        options.append("启用OCR")
    if hasattr(args, 'ocr_only') and args.ocr_only:
        options.append("仅OCR搜索")
    if args.dry_run:
        options.append("预览模式")
    if args.verbose:
        options.append("详细输出")

    if options:
        print(f"选项: {', '.join(options)}")

    # 显示OCR设置
    if hasattr(args, 'enable_ocr') and (args.enable_ocr or args.ocr_only):
        print(f"OCR语言: {getattr(args, 'ocr_lang', 'chi_sim+eng')}")
        print(f"OCR置信度: {getattr(args, 'ocr_confidence', 60)}")
    
    print("=" * 20)
