#!/usr/bin/env python3
"""
关键词归类功能测试脚本

测试新增的关键词按匹配状态归类功能：
- keywords_by_status.successful: 成功高亮的关键词
- keywords_by_status.failed: 未被高亮的关键词
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, get_logger

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = get_logger()


def create_classification_test_config():
    """创建归类测试配置文件"""
    config = {
        "keywords": [
            {
                "id": "existing_keyword_1",
                "text": "审核",
                "color": "red",
                "opacity": 0.8
            },
            {
                "id": "existing_keyword_2", 
                "text": "应用",
                "color": "green",
                "opacity": 0.6,
                "context_phrase": "苹果ipa应用"
            },
            {
                "id": "existing_keyword_3",
                "text": "提交",
                "color": "blue",
                "opacity": 0.7
            },
            {
                "id": "not_found_keyword_1",
                "text": "这个关键词不存在",
                "color": "purple",
                "opacity": 0.5
            },
            {
                "id": "not_found_keyword_2",
                "text": "另一个不存在的关键词",
                "color": "orange",
                "opacity": 0.4
            }
        ],
        "global_options": {
            "output_suffix": "_classification_test"
        }
    }
    
    config_file = project_root / "examples" / "classification_test_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    return config_file


def test_keywords_classification():
    """测试关键词归类功能"""
    print("=== 关键词归类功能测试 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    try:
        # 方式1：配置文件测试
        print("\n=== 配置文件方式测试 ===")
        config_file = create_classification_test_config()
        
        highlighter1 = MultiKeywordHighlighter(str(test_file))
        highlighter1.load_config_file(str(config_file))
        
        print("配置的关键词:")
        for i, config in enumerate(highlighter1.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else ""
            print(f"  {i}. [{config.id}] '{config.text}' -> {config.color}{context_info}")
        
        result1 = highlighter1.process()
        
        if result1['success']:
            print(f"\n✅ 处理成功！总共添加 {result1['total_highlights']} 个高亮")
            
            # 检查新的归类结果
            if 'keywords_by_status' in result1:
                keywords_by_status = result1['keywords_by_status']
                
                print(f"\n📊 关键词归类结果:")
                
                # 显示成功的关键词
                successful = keywords_by_status.get('successful', {})
                print(f"\n✅ 成功高亮的关键词 ({len(successful)} 个):")
                for keyword_id, stats in successful.items():
                    print(f"  [{keyword_id}] '{stats['text']}' -> {stats['highlights_added']} 个高亮")
                
                # 显示失败的关键词
                failed = keywords_by_status.get('failed', {})
                print(f"\n❌ 未被高亮的关键词 ({len(failed)} 个):")
                for keyword_id, stats in failed.items():
                    print(f"  [{keyword_id}] '{stats['text']}' -> {stats['status']}")
                
                # 验证归类的正确性
                print(f"\n🔍 归类验证:")
                total_classified = len(successful) + len(failed)
                total_keywords = len(result1['keywords_by_id'])
                
                if total_classified == total_keywords:
                    print(f"✅ 归类完整：{total_classified} = {total_keywords}")
                else:
                    print(f"❌ 归类不完整：{total_classified} ≠ {total_keywords}")
                
                # 验证归类的准确性
                classification_correct = True
                for keyword_id, stats in successful.items():
                    if stats['highlights_added'] <= 0:
                        print(f"❌ 错误归类：{keyword_id} 在成功组但没有高亮")
                        classification_correct = False
                
                for keyword_id, stats in failed.items():
                    if stats['highlights_added'] > 0:
                        print(f"❌ 错误归类：{keyword_id} 在失败组但有高亮")
                        classification_correct = False
                
                if classification_correct:
                    print("✅ 归类准确性验证通过")
                else:
                    print("❌ 归类准确性验证失败")
            
            else:
                print("❌ 缺少 keywords_by_status 字段")
        
        else:
            print(f"❌ 处理失败: {result1['error']}")
        
        # 方式2：编程API测试
        print(f"\n=== 编程API方式测试 ===")
        highlighter2 = MultiKeywordHighlighter(str(test_file))
        
        # 添加混合的关键词（有些存在，有些不存在）
        id1 = highlighter2.add_keyword("审核", color="red", id="api_existing_1")
        id2 = highlighter2.add_keyword("应用", color="green", id="api_existing_2", context_phrase="苹果ipa应用")
        id3 = highlighter2.add_keyword("不存在关键词1", color="blue", id="api_not_found_1")
        id4 = highlighter2.add_keyword("不存在关键词2", color="purple", id="api_not_found_2")
        
        print(f"添加的关键词ID: {id1}, {id2}, {id3}, {id4}")
        
        result2 = highlighter2.process()
        
        if result2['success']:
            print(f"✅ API处理成功！")
            
            # 使用归类结果进行简单的成功/失败检查
            keywords_by_status = result2.get('keywords_by_status', {})
            successful = keywords_by_status.get('successful', {})
            failed = keywords_by_status.get('failed', {})
            
            print(f"\n📋 简化的结果查看:")
            print(f"成功的关键词: {[stats['text'] for stats in successful.values()]}")
            print(f"失败的关键词: {[stats['text'] for stats in failed.values()]}")
            
            # 按ID快速查找
            print(f"\n🔍 按ID快速查找:")
            test_ids = [id1, id2, id3, id4]
            for test_id in test_ids:
                if test_id in successful:
                    print(f"  [{test_id}]: ✅ 成功")
                elif test_id in failed:
                    print(f"  [{test_id}]: ❌ 失败")
                else:
                    print(f"  [{test_id}]: ❓ 未找到")
        
        else:
            print(f"❌ API处理失败: {result2['error']}")
        
        # 方式3：展示使用场景
        print(f"\n=== 实用场景演示 ===")
        if result1['success']:
            keywords_by_status = result1['keywords_by_status']
            
            # 场景1：只关心成功的关键词
            print("场景1 - 只查看成功高亮的关键词:")
            successful_texts = [stats['text'] for stats in keywords_by_status['successful'].values()]
            print(f"  成功高亮: {successful_texts}")
            
            # 场景2：只关心失败的关键词
            print("场景2 - 只查看未被高亮的关键词:")
            failed_texts = [stats['text'] for stats in keywords_by_status['failed'].values()]
            print(f"  未被高亮: {failed_texts}")
            
            # 场景3：统计信息
            print("场景3 - 快速统计:")
            print(f"  成功率: {len(keywords_by_status['successful'])}/{len(result1['keywords_by_id'])} = {len(keywords_by_status['successful'])/len(result1['keywords_by_id'])*100:.1f}%")
    
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 关键词归类功能测试完成！")
    return True


if __name__ == "__main__":
    test_keywords_classification()
    
    print("\n" + "="*60)
    print("关键词归类功能优势:")
    print("1. ✅ 成功/失败关键词分组清晰")
    print("2. ✅ 便于快速查看特定状态的关键词")
    print("3. ✅ 支持按ID或按文本快速查找")
    print("4. ✅ 简化了结果处理逻辑")
    print("5. ✅ 保持原有 keywords_by_id 的完整性")
    print("="*60)
