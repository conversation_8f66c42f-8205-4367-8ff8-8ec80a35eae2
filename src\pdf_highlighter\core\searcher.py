"""
文本搜索逻辑模块

提供高级文本搜索功能，支持正则表达式、大小写敏感等选项。
"""

import re
from typing import List, Tuple, Optional, Union
import pymupdf


class TextSearcher:
    """文本搜索器
    
    提供多种搜索模式和选项的文本搜索功能。
    """
    
    def __init__(self, document: pymupdf.Document):
        """初始化文本搜索器
        
        Args:
            document: PyMuPDF文档对象
        """
        self.document = document
    
    def search(self, keyword: str, 
               pages: Optional[List[int]] = None,
               case_sensitive: bool = False,
               regex: bool = False,
               whole_word: bool = False) -> List[Tuple[int, List]]:
        """搜索关键词
        
        Args:
            keyword: 要搜索的关键词或正则表达式
            pages: 指定搜索的页码列表，None表示搜索所有页面
            case_sensitive: 是否区分大小写
            regex: 是否启用正则表达式模式
            whole_word: 是否只匹配完整单词
            
        Returns:
            List[Tuple[int, List]]: 每个元组包含(页码, quads列表)
            
        Raises:
            ValueError: 正则表达式无效或其他搜索参数错误
        """
        if not keyword.strip():
            raise ValueError("搜索关键词不能为空")
        
        results = []
        
        # 确定要搜索的页面范围
        if pages is None:
            page_range = range(len(self.document))
        else:
            page_range = [p - 1 for p in pages if 0 < p <= len(self.document)]
        
        for page_num in page_range:
            page = self.document[page_num]
            
            if regex:
                quads = self._regex_search(page, keyword, case_sensitive, whole_word)
            else:
                quads = self._text_search(page, keyword, case_sensitive, whole_word)
            
            if quads:
                results.append((page_num + 1, quads))  # 转换回1基索引
        
        return results
    
    def _text_search(self, page, keyword: str, case_sensitive: bool, whole_word: bool) -> List:
        """普通文本搜索
        
        Args:
            page: PyMuPDF页面对象
            keyword: 搜索关键词
            case_sensitive: 是否区分大小写
            whole_word: 是否只匹配完整单词
            
        Returns:
            List: 匹配位置的quads列表
        """
        if whole_word:
            # 使用正则表达式实现完整单词匹配
            pattern = r'\b' + re.escape(keyword) + r'\b'
            return self._regex_search(page, pattern, case_sensitive, False)
        else:
            # 使用PyMuPDF内置搜索
            search_flags = 0 if case_sensitive else pymupdf.TEXT_PRESERVE_WHITESPACE
            return page.search_for(keyword, quads=True, flags=search_flags)
    
    def _regex_search(self, page, pattern: str, case_sensitive: bool, whole_word: bool) -> List:
        """正则表达式搜索
        
        Args:
            page: PyMuPDF页面对象
            pattern: 正则表达式模式
            case_sensitive: 是否区分大小写
            whole_word: 是否只匹配完整单词（此参数在正则模式下被忽略）
            
        Returns:
            List: 匹配位置的quads列表
        """
        try:
            flags = 0 if case_sensitive else re.IGNORECASE
            regex_pattern = re.compile(pattern, flags)
        except re.error as e:
            raise ValueError(f"无效的正则表达式: {e}")
        
        # 获取页面文本和词汇信息
        words = page.get_text("words")
        quads = []
        
        for word in words:
            word_text = word[4]  # 词汇文本
            if regex_pattern.search(word_text):
                # 创建quad对象
                x0, y0, x1, y1 = word[:4]
                quad = pymupdf.Quad(
                    pymupdf.Point(x0, y0),  # 左上
                    pymupdf.Point(x1, y0),  # 右上
                    pymupdf.Point(x0, y1),  # 左下
                    pymupdf.Point(x1, y1)   # 右下
                )
                quads.append(quad)
        
        return quads
    
    def search_multiple_keywords(self, keywords: List[str], 
                                pages: Optional[List[int]] = None,
                                case_sensitive: bool = False,
                                regex: bool = False,
                                whole_word: bool = False) -> dict:
        """搜索多个关键词
        
        Args:
            keywords: 关键词列表
            pages: 指定搜索的页码列表
            case_sensitive: 是否区分大小写
            regex: 是否启用正则表达式模式
            whole_word: 是否只匹配完整单词
            
        Returns:
            dict: 每个关键词的搜索结果，格式为 {keyword: [(page, quads), ...]}
        """
        results = {}
        
        for keyword in keywords:
            try:
                results[keyword] = self.search(
                    keyword, pages, case_sensitive, regex, whole_word
                )
            except ValueError as e:
                results[keyword] = f"搜索错误: {e}"
        
        return results
    
    def get_search_statistics(self, search_results: List[Tuple[int, List]]) -> dict:
        """获取搜索统计信息
        
        Args:
            search_results: 搜索结果
            
        Returns:
            dict: 统计信息，包含总匹配数、页面数等
        """
        total_matches = sum(len(quads) for _, quads in search_results)
        pages_with_matches = len(search_results)
        pages_list = [page for page, _ in search_results]
        
        return {
            'total_matches': total_matches,
            'pages_with_matches': pages_with_matches,
            'pages_list': pages_list,
            'matches_per_page': {page: len(quads) for page, quads in search_results}
        }
    
    def highlight_search_context(self, page_num: int, keyword: str, 
                                context_lines: int = 2) -> str:
        """获取搜索结果的上下文文本
        
        Args:
            page_num: 页码（1基索引）
            keyword: 搜索关键词
            context_lines: 上下文行数
            
        Returns:
            str: 包含上下文的文本
        """
        if page_num < 1 or page_num > len(self.document):
            raise ValueError(f"页码超出范围: {page_num}")
        
        page = self.document[page_num - 1]
        text = page.get_text()
        lines = text.split('\n')
        
        context_text = []
        for i, line in enumerate(lines):
            if keyword.lower() in line.lower():
                # 添加上下文
                start = max(0, i - context_lines)
                end = min(len(lines), i + context_lines + 1)
                
                for j in range(start, end):
                    prefix = ">>> " if j == i else "    "
                    context_text.append(f"{prefix}{lines[j]}")
                context_text.append("---")
        
        return '\n'.join(context_text)
