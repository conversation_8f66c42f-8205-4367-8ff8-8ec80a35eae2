"""
PDF高亮处理核心模块

基于PyMuPDF实现PDF文件的关键词搜索和高亮注释功能。
"""

import os
import re
from typing import List, Optional, Tuple, Union
import pymupdf

from pdf_highlighter.config.settings import is_ocr_enabled
from pdf_highlighter.utils.logger import get_logger


class PDFHighlighter:
    """PDF关键词高亮处理器
    
    支持多格式PDF文件的关键词搜索和高亮注释功能。
    """
    
    def __init__(self, pdf_path: str, enable_ocr: Optional[bool] = None):
        """初始化PDF高亮处理器

        Args:
            pdf_path: PDF文件路径
            enable_ocr: 是否启用OCR功能，None表示使用配置文件设置

        Raises:
            FileNotFoundError: PDF文件不存在
            ValueError: 文件不是有效的PDF格式
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")

        try:
            self.doc = pymupdf.open(pdf_path)
            self.pdf_path = pdf_path
            self.logger = get_logger()

            # 设置OCR功能
            if enable_ocr is None:
                self.enable_ocr = is_ocr_enabled()
            else:
                self.enable_ocr = enable_ocr

            # 延迟加载OCR处理器
            self._ocr_processor = None

        except Exception as e:
            raise ValueError(f"无法打开PDF文件: {e}")
    
    def search_keywords(self, keyword: str, pages: Optional[List[int]] = None,
                       case_sensitive: bool = False, regex: bool = False,
                       enable_ocr: Optional[bool] = None) -> List[Tuple[int, List]]:
        """搜索关键词在PDF中的位置

        Args:
            keyword: 要搜索的关键词
            pages: 指定搜索的页码列表，None表示搜索所有页面
            case_sensitive: 是否区分大小写
            regex: 是否启用正则表达式模式
            enable_ocr: 是否启用OCR搜索图片中的文字，None表示使用实例设置

        Returns:
            List[Tuple[int, List]]: 每个元组包含(页码, quads列表)
        """
        results = []

        # 确定是否启用OCR
        use_ocr = enable_ocr if enable_ocr is not None else self.enable_ocr

        # 确定要搜索的页面范围
        if pages is None:
            page_range = range(len(self.doc))
        else:
            page_range = [p - 1 for p in pages if 0 < p <= len(self.doc)]  # 转换为0基索引

        for page_num in page_range:
            page = self.doc[page_num]
            quads = []

            # 搜索文本内容
            if regex:
                # 正则表达式搜索
                text_quads = self._regex_search(page, keyword, case_sensitive)
            else:
                # 普通文本搜索
                search_flags = 0 if case_sensitive else pymupdf.TEXT_PRESERVE_WHITESPACE
                text_quads = page.search_for(keyword, quads=True, flags=search_flags)

            quads.extend(text_quads)

            # 如果启用OCR，搜索图片中的文字
            if use_ocr:
                try:
                    ocr_matches = self._search_in_images(page, keyword, case_sensitive, regex)
                    # 将OCR结果转换为Quad对象
                    for bbox in ocr_matches:
                        x0, y0, x1, y1 = bbox
                        quad = pymupdf.Quad(
                            pymupdf.Point(x0, y0),  # 左上
                            pymupdf.Point(x1, y0),  # 右上
                            pymupdf.Point(x0, y1),  # 左下
                            pymupdf.Point(x1, y1)   # 右下
                        )
                        quads.append(quad)
                except Exception as e:
                    self.logger.warning(f"页面{page_num + 1}的OCR搜索失败: {e}")

            if quads:
                results.append((page_num + 1, quads))  # 转换回1基索引

        return results
    
    def _regex_search(self, page, pattern: str, case_sensitive: bool) -> List:
        """使用正则表达式搜索文本
        
        Args:
            page: PyMuPDF页面对象
            pattern: 正则表达式模式
            case_sensitive: 是否区分大小写
            
        Returns:
            List: 匹配位置的quads列表
        """
        try:
            flags = 0 if case_sensitive else re.IGNORECASE
            regex_pattern = re.compile(pattern, flags)
        except re.error as e:
            raise ValueError(f"无效的正则表达式: {e}")
        
        # 获取页面文本和词汇信息
        words = page.get_text("words")
        quads = []
        
        for word in words:
            word_text = word[4]  # 词汇文本
            if regex_pattern.search(word_text):
                # 创建quad对象，word[:4]是[x0, y0, x1, y1]格式
                x0, y0, x1, y1 = word[:4]
                # 创建矩形的四个角点
                quad = pymupdf.Quad(
                    pymupdf.Point(x0, y0),  # 左上
                    pymupdf.Point(x1, y0),  # 右上
                    pymupdf.Point(x0, y1),  # 左下
                    pymupdf.Point(x1, y1)   # 右下
                )
                quads.append(quad)
        
        return quads

    def _get_ocr_processor(self):
        """获取OCR处理器实例（延迟加载）"""
        if self._ocr_processor is None:
            try:
                from pdf_highlighter.core.ocr_processor import OCRProcessor
                self._ocr_processor = OCRProcessor()
            except ImportError as e:
                self.logger.error(f"无法导入OCR处理器: {e}")
                raise
        return self._ocr_processor

    def _search_in_images(self, page: pymupdf.Page, keyword: str,
                         case_sensitive: bool, regex: bool) -> List[Tuple[float, float, float, float]]:
        """在页面图片中搜索文字

        Args:
            page: PyMuPDF页面对象
            keyword: 搜索关键词
            case_sensitive: 是否区分大小写
            regex: 是否使用正则表达式

        Returns:
            List[Tuple]: 匹配文字的PDF页面坐标列表
        """
        try:
            ocr_processor = self._get_ocr_processor()
            return ocr_processor.search_text_in_images(page, keyword, case_sensitive, regex)
        except Exception as e:
            self.logger.error(f"OCR搜索失败: {e}")
            return []

    def search_in_images_only(self, keyword: str, pages: Optional[List[int]] = None,
                             case_sensitive: bool = False, regex: bool = False) -> List[Tuple[int, List]]:
        """仅在图片中搜索关键词

        Args:
            keyword: 要搜索的关键词
            pages: 指定搜索的页码列表，None表示搜索所有页面
            case_sensitive: 是否区分大小写
            regex: 是否启用正则表达式模式

        Returns:
            List[Tuple[int, List]]: 每个元组包含(页码, quads列表)
        """
        if not self.enable_ocr:
            self.logger.warning("OCR功能未启用")
            return []

        results = []

        # 确定要搜索的页面范围
        if pages is None:
            page_range = range(len(self.doc))
        else:
            page_range = [p - 1 for p in pages if 0 < p <= len(self.doc)]

        for page_num in page_range:
            page = self.doc[page_num]

            try:
                ocr_matches = self._search_in_images(page, keyword, case_sensitive, regex)
                quads = []

                # 将OCR结果转换为Quad对象
                for bbox in ocr_matches:
                    x0, y0, x1, y1 = bbox
                    quad = pymupdf.Quad(
                        pymupdf.Point(x0, y0),  # 左上
                        pymupdf.Point(x1, y0),  # 右上
                        pymupdf.Point(x0, y1),  # 左下
                        pymupdf.Point(x1, y1)   # 右下
                    )
                    quads.append(quad)

                if quads:
                    results.append((page_num + 1, quads))

            except Exception as e:
                self.logger.warning(f"页面{page_num + 1}的OCR搜索失败: {e}")

        return results

    def add_highlights(self, search_results: List[Tuple[int, List]],
                      color: Tuple[float, float, float] = (1, 1, 0),
                      opacity: float = 0.5,
                      occurrence: Optional[int] = None) -> int:
        """添加高亮注释
        
        Args:
            search_results: 搜索结果，来自search_keywords方法
            color: 高亮颜色，RGB值(0-1)，默认黄色
            opacity: 透明度(0-1)，默认0.5
            occurrence: 指定高亮第几个匹配项，None表示高亮所有匹配项
            
        Returns:
            int: 实际添加的高亮注释数量
        """
        highlight_count = 0
        total_matches = 0
        
        for page_num, quads in search_results:
            page = self.doc[page_num - 1]  # 转换为0基索引
            
            for quad in quads:
                total_matches += 1
                
                # 如果指定了occurrence，只高亮特定的匹配项
                if occurrence is not None and total_matches != occurrence:
                    continue
                
                # 添加高亮注释
                annot = page.add_highlight_annot(quad)
                annot.set_colors(stroke=color)
                annot.set_opacity(opacity)
                annot.update()
                highlight_count += 1
                
                # 如果只高亮特定匹配项，找到后就退出
                if occurrence is not None and total_matches == occurrence:
                    return highlight_count
        
        return highlight_count
    
    def save_pdf(self, output_path: str, incremental: bool = False) -> None:
        """保存处理后的PDF文件
        
        Args:
            output_path: 输出文件路径
            incremental: 是否使用增量保存
            
        Raises:
            PermissionError: 没有写入权限
            OSError: 保存失败
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            self.doc.save(output_path, incremental=incremental)
        except Exception as e:
            raise OSError(f"保存PDF文件失败: {e}")
    
    def get_page_count(self) -> int:
        """获取PDF总页数"""
        return len(self.doc)
    
    def close(self) -> None:
        """关闭PDF文档"""
        if hasattr(self, 'doc') and self.doc:
            self.doc.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
        return False
