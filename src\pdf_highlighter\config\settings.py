"""
默认配置和设置管理模块

提供PDF高亮工具的默认配置和设置管理功能。
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
import json


class Settings:
    """配置管理类
    
    管理PDF高亮工具的所有配置项。
    """
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 高亮设置
        'highlight': {
            'default_color': (1, 1, 0),  # 黄色 RGB
            'default_opacity': 0.5,
            'color_presets': {
                'yellow': (1, 1, 0),
                'red': (1, 0, 0),
                'green': (0, 1, 0),
                'blue': (0, 0, 1),
                'orange': (1, 0.5, 0),
                'purple': (0.5, 0, 1),
                'pink': (1, 0, 1),
                'cyan': (0, 1, 1),
                'gray': (0.5, 0.5, 0.5),
                'black': (0, 0, 0),
                'white': (1, 1, 1)
            },
            'opacity_range': (0.0, 1.0),
            'max_highlights_per_page': 1000
        },
        
        # 搜索设置
        'search': {
            'max_keyword_length': 1000,
            'default_case_sensitive': False,
            'default_regex_mode': False,
            'default_whole_word': False,
            'context_lines': 2,
            'max_search_results': 10000
        },
        
        # 文件设置
        'files': {
            'output_suffix': '_highlighted',
            'backup_original': False,
            'auto_create_dirs': True,
            'max_file_size_mb': 100,
            'supported_extensions': ['.pdf'],
            'temp_dir': None  # None表示使用系统临时目录
        },
        
        # 日志设置
        'logging': {
            'level': 'INFO',
            'console_output': True,
            'file_output': False,
            'log_file': 'pdf_highlighter.log',
            'max_log_size_mb': 10,
            'log_backup_count': 5,
            'date_format': '%Y-%m-%d %H:%M:%S',
            'message_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        
        # 性能设置
        'performance': {
            'max_memory_usage_mb': 512,
            'enable_lazy_loading': True,
            'cache_search_results': True,
            'max_cache_size': 100,
            'timeout_seconds': 300
        },
        
        # 用户界面设置
        'ui': {
            'language': 'zh_CN',
            'show_progress': True,
            'verbose_by_default': False,
            'confirm_overwrite': True,
            'show_statistics': True
        },

        # OCR设置
        'ocr': {
            'enabled': False,
            'language': 'chi_sim+eng',  # 中文简体+英文
            'confidence_threshold': 60,  # OCR置信度阈值(0-100)
            'timeout_seconds': 30,  # OCR超时时间
            'image_preprocessing': {
                'enable_denoising': True,  # 启用去噪
                'enable_binarization': True,  # 启用二值化
                'scale_factor': 2.0,  # 图片放大倍数，提高OCR精度
                'dpi': 300  # 图片DPI设置
            },
            'cache_enabled': True,  # 启用OCR结果缓存
            'max_cache_size': 100,  # 最大缓存条目数
            'parallel_processing': True,  # 启用并行处理
            'max_workers': 4  # 最大并行工作线程数
        }
    }
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路径，None表示使用默认配置
        """
        self.config_file = Path(config_file) if config_file else None
        self._config = self.DEFAULT_CONFIG.copy()
        
        # 加载配置文件
        if self.config_file and self.config_file.exists():
            self.load_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'highlight.default_color'
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置配置项
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def update(self, config_dict: Dict[str, Any]) -> None:
        """批量更新配置
        
        Args:
            config_dict: 配置字典
        """
        self._deep_update(self._config, config_dict)
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict) -> None:
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def load_config(self, config_file: Optional[Union[str, Path]] = None) -> None:
        """从文件加载配置
        
        Args:
            config_file: 配置文件路径，None表示使用初始化时的文件
        """
        if config_file:
            self.config_file = Path(config_file)
        
        if not self.config_file or not self.config_file.exists():
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
            
            self.update(user_config)
            
        except (json.JSONDecodeError, IOError) as e:
            from pdf_highlighter.utils.exceptions import ConfigurationError
            raise ConfigurationError(f"加载配置文件失败: {e}", config_key=str(self.config_file))
    
    def save_config(self, config_file: Optional[Union[str, Path]] = None) -> None:
        """保存配置到文件
        
        Args:
            config_file: 配置文件路径，None表示使用初始化时的文件
        """
        if config_file:
            self.config_file = Path(config_file)
        
        if not self.config_file:
            raise ValueError("未指定配置文件路径")
        
        # 确保目录存在
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
        except IOError as e:
            from pdf_highlighter.utils.exceptions import ConfigurationError
            raise ConfigurationError(f"保存配置文件失败: {e}", config_key=str(self.config_file))
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self._config = self.DEFAULT_CONFIG.copy()
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def validate_config(self) -> bool:
        """验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证高亮颜色
            color = self.get('highlight.default_color')
            if not (isinstance(color, (list, tuple)) and len(color) == 3):
                return False
            
            # 验证透明度
            opacity = self.get('highlight.default_opacity')
            if not (isinstance(opacity, (int, float)) and 0 <= opacity <= 1):
                return False
            
            # 验证关键词长度限制
            max_length = self.get('search.max_keyword_length')
            if not (isinstance(max_length, int) and max_length > 0):
                return False
            
            # 验证文件大小限制
            max_size = self.get('files.max_file_size_mb')
            if not (isinstance(max_size, (int, float)) and max_size > 0):
                return False
            
            return True
            
        except Exception:
            return False


# 全局配置实例
_settings = None


def get_settings(config_file: Optional[Union[str, Path]] = None) -> Settings:
    """获取全局配置实例
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        Settings: 配置实例
    """
    global _settings
    
    if _settings is None:
        _settings = Settings(config_file)
    
    return _settings


def load_user_config(config_file: Union[str, Path]) -> None:
    """加载用户配置文件
    
    Args:
        config_file: 用户配置文件路径
    """
    settings = get_settings()
    settings.load_config(config_file)


def get_config(key: str, default: Any = None) -> Any:
    """获取配置项的便捷函数
    
    Args:
        key: 配置键
        default: 默认值
        
    Returns:
        Any: 配置值
    """
    return get_settings().get(key, default)


def set_config(key: str, value: Any) -> None:
    """设置配置项的便捷函数
    
    Args:
        key: 配置键
        value: 配置值
    """
    get_settings().set(key, value)


# 常用配置项的快捷访问函数
def get_default_color() -> tuple:
    """获取默认高亮颜色"""
    return get_config('highlight.default_color')


def get_default_opacity() -> float:
    """获取默认透明度"""
    return get_config('highlight.default_opacity')


def get_output_suffix() -> str:
    """获取输出文件后缀"""
    return get_config('files.output_suffix')


def get_max_keyword_length() -> int:
    """获取关键词最大长度"""
    return get_config('search.max_keyword_length')


def is_debug_enabled() -> bool:
    """检查是否启用调试模式"""
    return get_config('logging.level', 'INFO').upper() == 'DEBUG'


def is_ocr_enabled() -> bool:
    """检查是否启用OCR功能"""
    return get_config('ocr.enabled', False)


def get_ocr_language() -> str:
    """获取OCR语言设置"""
    return get_config('ocr.language', 'chi_sim+eng')


def get_ocr_confidence_threshold() -> int:
    """获取OCR置信度阈值"""
    return get_config('ocr.confidence_threshold', 60)


def get_ocr_timeout() -> int:
    """获取OCR超时时间"""
    return get_config('ocr.timeout_seconds', 30)
