{"description": "增强的配置文件示例，展示关键词ID功能", "keywords": [{"id": "frontend_development", "text": "前端开发", "color": "yellow", "opacity": 0.6, "pages": "1-5", "priority": 1}, {"id": "backend_development", "text": "后端开发", "color": "blue", "opacity": 0.7, "pages": "6-10", "priority": 1}, {"id": "review_process_page3", "text": "审核", "color": "red", "opacity": 0.8, "pages": "3", "context_phrase": "添加以供审核", "priority": 1}, {"id": "review_process_page2", "text": "审核", "color": "orange", "opacity": 0.7, "pages": "2", "context_phrase": "添加审核信息", "priority": 1}, {"id": "general_review", "text": "审核", "color": "yellow", "opacity": 0.6, "pages": "1,4-6", "priority": 2}, {"id": "mobile_app", "text": "应用", "color": "green", "opacity": 0.6, "context_phrase": "苹果ipa应用", "priority": 1}, {"id": "programming_langs", "text": "Python|Java|JavaScript", "color": "purple", "opacity": 0.5, "regex": true, "priority": 2}, {"text": "API接口", "color": "cyan", "opacity": 0.4, "case_sensitive": true, "whole_word": true, "priority": 3}], "global_options": {"context_fallback_strategy": "fallback_to_normal", "output_suffix": "_enhanced_with_ids"}, "id_feature_notes": ["id字段是可选的，如果不提供会自动生成", "手动指定的ID必须在配置文件中唯一", "自动生成的ID基于关键词内容的哈希值", "ID用于结果统计和追踪，便于分析处理结果", "支持相同关键词文本配置不同的ID和参数"], "result_format_example": {"keywords_by_id": {"frontend_development": {"id": "frontend_development", "text": "前端开发", "matches_found": 5, "highlights_added": 5, "status": "success"}, "review_process_page3": {"id": "review_process_page3", "text": "审核", "matches_found": 1, "highlights_added": 1, "status": "success"}}, "summary": {"total_keywords": 8, "successful_keywords": 6, "failed_keywords": 2, "skipped_keywords": 0}}}