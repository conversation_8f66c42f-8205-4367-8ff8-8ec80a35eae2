#!/usr/bin/env python3
"""
上下文匹配功能实际演示脚本

使用更实际的上下文短语来演示功能
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, PDFHighlighter, get_logger

# 设置日志
logger = get_logger()


def demo_context_matching():
    """演示上下文匹配功能"""
    print("=== 上下文匹配功能实际演示 ===\n")
    
    # 检查是否有测试PDF文件
    test_files = list(project_root.glob("*.pdf"))
    if not test_files:
        print("未找到测试PDF文件，请在项目根目录放置一个PDF文件进行测试")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file}")
    
    # 先分析PDF内容，找到一些实际存在的文本
    print("\n分析PDF内容...")
    try:
        with PDFHighlighter(str(test_file)) as analyzer:
            total_pages = analyzer.get_page_count()
            print(f"PDF总页数: {total_pages}")
            
            # 提取前几页的文本内容作为参考
            sample_texts = []
            for page_num in range(min(3, total_pages)):
                page = analyzer.doc[page_num]
                text = page.get_text()
                if text.strip():
                    sample_texts.append(text[:200])  # 取前200个字符
                    print(f"第{page_num + 1}页文本片段: {text[:100]}...")
    
    except Exception as e:
        print(f"分析PDF内容失败: {e}")
        return
    
    try:
        # 创建多关键词高亮处理器
        highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 方式1：使用编程API添加上下文匹配关键词
        print("\n方式1：编程API添加上下文匹配关键词")
        
        # 使用一些通用的上下文短语
        highlighter.add_keyword("的", color="yellow", context_phrase="的文档")
        highlighter.add_keyword("是", color="blue", context_phrase="这是")
        highlighter.add_keyword("在", color="green", context_phrase="在这")
        highlighter.add_keyword("了", color="red", context_phrase="了解")
        highlighter.add_keyword("和", color="orange")  # 没有上下文限制，作为对比
        
        # 显示配置
        print("配置的关键词:")
        for i, config in enumerate(highlighter.keyword_configs, 1):
            context_info = f" (上下文: {config.context_phrase})" if config.context_phrase else " (无上下文限制)"
            print(f"  {i}. '{config.text}' -> {config.color}{context_info}")
        
        # 执行处理
        output_file = project_root / f"demo_context_highlighted_{test_file.name}"
        print(f"\n开始处理，输出文件: {output_file}")
        
        result = highlighter.process(str(output_file))
        
        if result['success']:
            print(f"✅ 处理成功！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print("\n各关键词处理结果:")
            for keyword, info in result['keywords_processed'].items():
                print(f"  '{keyword}': 找到 {info['matches_found']} 个匹配，添加 {info['highlights_added']} 个高亮")
        else:
            print(f"❌ 处理失败: {result['error']}")
        
        # 方式2：创建一个实用的配置文件示例
        print(f"\n方式2：创建实用的配置文件示例")
        practical_config = {
            "keywords": [
                {
                    "text": "审核",
                    "color": "red",
                    "opacity": 0.8,
                    "pages": "3",
                    "context_phrase": "添加以供审核"
                },
                {
                    "text": "确认",
                    "color": "blue",
                    "opacity": 0.7,
                    "context_phrase": "请确认无误"
                },
                {
                    "text": "提交",
                    "color": "green",
                    "opacity": 0.6,
                    "context_phrase": "点击提交"
                },
                {
                    "text": "保存",
                    "color": "orange",
                    "opacity": 0.5,
                    "context_phrase": "自动保存"
                }
            ],
            "global_options": {
                "output_suffix": "_context_demo"
            }
        }
        
        config_file = project_root / "examples" / "practical_context_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(practical_config, f, indent=2, ensure_ascii=False)
        
        print(f"实用配置文件已创建: {config_file}")
        print("配置文件内容:")
        for keyword in practical_config['keywords']:
            print(f"  '{keyword['text']}' 在上下文 '{keyword['context_phrase']}' 中高亮为 {keyword['color']}")
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ 上下文匹配功能演示完成！")
    return True


if __name__ == "__main__":
    demo_context_matching()
    
    print("\n" + "="*70)
    print("上下文匹配功能使用场景:")
    print("1. 📋 合同审核：只高亮特定条款中的关键词")
    print("2. 📝 文档校对：只标记特定语境中的术语")
    print("3. 🔍 精确搜索：避免误匹配常见词汇")
    print("4. 📊 数据分析：只关注特定上下文中的数据")
    print("5. 📚 学术研究：精确标注特定概念的使用")
    print("\n配置示例:")
    print('{"text": "审核", "context_phrase": "添加以供审核", "color": "red"}')
    print("只会高亮'添加以供审核'中的'审核'，而不会高亮其他地方的'审核'")
    print("="*70)
