#!/usr/bin/env python3
"""
多关键词高亮示例

演示如何在一次执行中对多个关键词使用不同颜色进行高亮，
支持配置文件和命令行两种方式。
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import MultiKeywordHighlighter, KeywordConfig, get_logger


def example_1_programmatic_api():
    """示例1：使用编程API进行多关键词高亮"""
    print("=== 示例1：编程API多关键词高亮 ===")
    
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    try:
        # 创建多关键词高亮处理器
        multi_highlighter = MultiKeywordHighlighter(str(test_file))
        
        # 添加多个关键词配置
        keywords_config = [
            ("前端", "yellow", 0.6),
            ("后端", "blue", 0.7),
            ("工程师", "green", 0.5),
            ("开发", "orange", 0.8),
            ("Python", "#FF6B6B", 0.6)
        ]
        
        for keyword, color, opacity in keywords_config:
            multi_highlighter.add_keyword(
                text=keyword,
                color=color,
                opacity=opacity,
                case_sensitive=False
            )
        
        # 显示配置摘要
        config_summary = multi_highlighter.get_config_summary()
        print(f"配置了 {config_summary['keyword_count']} 个关键词:")
        for i, keyword_info in enumerate(config_summary['keywords'], 1):
            print(f"  {i}. '{keyword_info['text']}' -> {keyword_info['color']} (透明度: {keyword_info['opacity']})")
        
        # 执行处理
        output_file = project_root / "examples" / "multi_keyword_api.pdf"
        result = multi_highlighter.process(str(output_file))
        
        if result['success']:
            print(f"\n处理完成！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print(f"输出文件: {result['output_file']}")
            
            print("\n关键词处理详情:")
            for keyword, info in result['keywords_processed'].items():
                print(f"  '{keyword}': {info['matches_found']} 个匹配项 -> {info['highlights_added']} 个高亮 ({info['color']})")
        else:
            print(f"处理失败: {result['error']}")
            
    except Exception as e:
        print(f"示例1执行失败: {e}")


def example_2_config_file():
    """示例2：使用配置文件进行多关键词高亮"""
    print("\n=== 示例2：配置文件多关键词高亮 ===")
    
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    # 创建配置文件
    config_data = {
        "keywords": [
            {
                "text": "前端",
                "color": "yellow",
                "opacity": 0.6,
                "case_sensitive": False,
                "regex": False
            },
            {
                "text": "后端",
                "color": "blue", 
                "opacity": 0.7,
                "case_sensitive": False,
                "regex": False
            },
            {
                "text": "工程师|程序员",
                "color": "green",
                "opacity": 0.5,
                "case_sensitive": False,
                "regex": True
            },
            {
                "text": "开发",
                "color": "orange",
                "opacity": 0.8,
                "case_sensitive": False,
                "regex": False,
                "occurrence": 5  # 只高亮前5个匹配项
            },
            {
                "text": "API",
                "color": "#FF6B6B",
                "opacity": 0.6,
                "case_sensitive": True,
                "regex": False,
                "whole_word": True
            }
        ],
        "global_options": {
            "pages": None,
            "occurrence": None,
            "whole_word": False,
            "output_suffix": "_config_highlighted"
        }
    }
    
    # 保存配置文件
    config_file = project_root / "examples" / "multi_keyword_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print(f"配置文件已创建: {config_file}")
    
    try:
        # 使用配置文件创建处理器
        multi_highlighter = MultiKeywordHighlighter(str(test_file))
        multi_highlighter.load_config_file(config_file)
        
        # 显示配置摘要
        config_summary = multi_highlighter.get_config_summary()
        print(f"从配置文件加载了 {config_summary['keyword_count']} 个关键词:")
        for i, keyword_info in enumerate(config_summary['keywords'], 1):
            options = keyword_info['options']
            extra_info = []
            if options['regex']:
                extra_info.append("正则")
            if options['case_sensitive']:
                extra_info.append("区分大小写")
            if options['occurrence']:
                extra_info.append(f"前{options['occurrence']}个")
            
            extra_str = f" ({', '.join(extra_info)})" if extra_info else ""
            print(f"  {i}. '{keyword_info['text']}' -> {keyword_info['color']}{extra_str}")
        
        # 执行处理
        result = multi_highlighter.process()
        
        if result['success']:
            print(f"\n处理完成！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print(f"输出文件: {result['output_file']}")
            
            print("\n关键词处理详情:")
            for keyword, info in result['keywords_processed'].items():
                print(f"  '{keyword}': {info['matches_found']} 个匹配项 -> {info['highlights_added']} 个高亮")
        else:
            print(f"处理失败: {result['error']}")
            
    except Exception as e:
        print(f"示例2执行失败: {e}")


def example_3_keyword_config_class():
    """示例3：使用KeywordConfig类进行精细配置"""
    print("\n=== 示例3：KeywordConfig类精细配置 ===")
    
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    try:
        # 创建关键词配置对象
        keyword_configs = [
            KeywordConfig(
                text="前端开发",
                color=(1, 1, 0),  # RGB格式
                opacity=0.6,
                case_sensitive=False,
                regex=False
            ),
            KeywordConfig(
                text="React|Vue|Angular",
                color="#4CAF50",  # 十六进制格式
                opacity=0.7,
                case_sensitive=False,
                regex=True
            ),
            KeywordConfig(
                text="JavaScript",
                color="blue",  # 颜色名称
                opacity=0.5,
                case_sensitive=True,
                whole_word=True,
                occurrence=3  # 只高亮前3个
            )
        ]
        
        # 创建处理器并添加配置
        multi_highlighter = MultiKeywordHighlighter(str(test_file))
        
        for config in keyword_configs:
            multi_highlighter.keyword_configs.append(config)
        
        print(f"使用KeywordConfig配置了 {len(keyword_configs)} 个关键词:")
        for i, config in enumerate(keyword_configs, 1):
            print(f"  {i}. '{config.text}' -> {config.color} (透明度: {config.opacity})")
        
        # 执行处理
        output_file = project_root / "examples" / "multi_keyword_config_class.pdf"
        result = multi_highlighter.process(str(output_file))
        
        if result['success']:
            print(f"\n处理完成！")
            print(f"总共添加 {result['total_highlights']} 个高亮")
            print(f"输出文件: {result['output_file']}")
        else:
            print(f"处理失败: {result['error']}")
            
    except Exception as e:
        print(f"示例3执行失败: {e}")


def example_4_batch_processing():
    """示例4：批量处理多个文件"""
    print("\n=== 示例4：批量多关键词处理 ===")
    
    test_file = project_root / "test.pdf"
    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return
    
    # 模拟多个文件
    file_list = [str(test_file)] * 3
    
    # 通用关键词配置
    common_keywords = [
        ("前端", "yellow", 0.6),
        ("后端", "blue", 0.7),
        ("全栈", "green", 0.5)
    ]
    
    print(f"批量处理 {len(file_list)} 个文件，每个文件使用 {len(common_keywords)} 个关键词")
    
    results = []
    
    for i, file_path in enumerate(file_list, 1):
        print(f"\n处理文件 {i}/{len(file_list)}: {Path(file_path).name}")
        
        try:
            multi_highlighter = MultiKeywordHighlighter(file_path)
            
            # 添加关键词配置
            for keyword, color, opacity in common_keywords:
                multi_highlighter.add_keyword(
                    text=keyword,
                    color=color,
                    opacity=opacity
                )
            
            # 生成唯一的输出文件名
            output_file = project_root / "examples" / f"batch_multi_{i}.pdf"
            result = multi_highlighter.process(str(output_file))
            
            results.append(result)
            
            if result['success']:
                print(f"  成功: {result['total_highlights']} 个高亮")
            else:
                print(f"  失败: {result['error']}")
                
        except Exception as e:
            print(f"  错误: {e}")
            results.append({'success': False, 'error': str(e)})
    
    # 统计结果
    successful = sum(1 for r in results if r['success'])
    total_highlights = sum(r.get('total_highlights', 0) for r in results if r['success'])
    
    print(f"\n批量处理完成:")
    print(f"  成功文件: {successful}/{len(file_list)}")
    print(f"  总高亮数: {total_highlights}")


def main():
    """主函数"""
    print("多关键词高亮功能演示")
    print("=" * 60)
    
    # 设置日志
    logger = get_logger()
    
    # 创建输出目录
    examples_dir = project_root / "examples"
    examples_dir.mkdir(exist_ok=True)
    
    # 运行各种示例
    example_1_programmatic_api()
    example_2_config_file()
    example_3_keyword_config_class()
    example_4_batch_processing()
    
    print("\n" + "=" * 60)
    print("所有多关键词示例执行完成！")
    print(f"输出文件保存在: {examples_dir}")


if __name__ == "__main__":
    main()
