#!/usr/bin/env python3
"""
PDF高亮工具高级使用示例

演示pdf_highlighter的高级功能，包括批处理、配置管理、日志记录等。
"""

import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from pdf_highlighter import (
    PDFHighlighter, TextSearcher, ParameterValidator,
    get_logger, setup_logging, get_settings, get_config
)
from pdf_highlighter.utils.exceptions import PDFError, SearchError


class AdvancedPDFProcessor:
    """高级PDF处理器
    
    演示如何创建一个功能完整的PDF处理类。
    """
    
    def __init__(self, log_file: str = None):
        """初始化处理器
        
        Args:
            log_file: 日志文件路径
        """
        # 设置日志
        self.logger = setup_logging(
            log_file=log_file,
            level='INFO',
            debug=False
        )
        
        # 获取配置
        self.settings = get_settings()
        
        # 统计信息
        self.stats = {
            'processed_files': 0,
            'total_highlights': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }
    
    def validate_inputs(self, pdf_path: str, keywords: List[str], 
                       pages: str = None, colors: List[str] = None) -> Dict[str, Any]:
        """验证输入参数
        
        Args:
            pdf_path: PDF文件路径
            keywords: 关键词列表
            pages: 页码范围字符串
            colors: 颜色列表
            
        Returns:
            Dict: 验证后的参数
        """
        validated = {}
        
        try:
            # 验证PDF路径
            validated['pdf_path'] = ParameterValidator.validate_pdf_path(pdf_path)
            
            # 验证关键词
            validated['keywords'] = []
            for keyword in keywords:
                validated['keywords'].append(ParameterValidator.validate_keyword(keyword))
            
            # 验证颜色
            if colors:
                validated['colors'] = []
                for color in colors:
                    validated['colors'].append(ParameterValidator.validate_color(color))
            else:
                # 使用默认颜色
                default_colors = ['yellow', 'green', 'blue', 'orange', 'purple']
                validated['colors'] = [
                    ParameterValidator.validate_color(c) for c in default_colors[:len(keywords)]
                ]
            
            # 验证页码范围
            if pages:
                with PDFHighlighter(validated['pdf_path']) as highlighter:
                    total_pages = highlighter.get_page_count()
                    validated['pages'] = ParameterValidator.validate_pages(pages, total_pages)
            else:
                validated['pages'] = None
            
            return validated
            
        except Exception as e:
            self.logger.error(f"参数验证失败: {e}")
            raise
    
    def process_single_file(self, pdf_path: str, keywords: List[str], 
                          output_path: str = None, **options) -> Dict[str, Any]:
        """处理单个PDF文件
        
        Args:
            pdf_path: 输入PDF路径
            keywords: 关键词列表
            output_path: 输出PDF路径
            **options: 其他选项
            
        Returns:
            Dict: 处理结果
        """
        result = {
            'success': False,
            'input_file': pdf_path,
            'output_file': output_path,
            'highlights_added': 0,
            'keywords_found': {},
            'error': None
        }
        
        try:
            # 验证输入
            validated = self.validate_inputs(
                pdf_path, keywords, 
                options.get('pages'), 
                options.get('colors')
            )
            
            # 生成输出路径
            if not output_path:
                input_path = Path(pdf_path)
                output_path = input_path.parent / f"{input_path.stem}_highlighted{input_path.suffix}"
            
            result['output_file'] = str(output_path)
            
            # 处理PDF
            with PDFHighlighter(validated['pdf_path']) as highlighter:
                total_highlights = 0
                
                for i, keyword in enumerate(validated['keywords']):
                    self.logger.info(f"搜索关键词: '{keyword}'")
                    
                    # 搜索关键词
                    search_results = highlighter.search_keywords(
                        keyword,
                        pages=validated['pages'],
                        case_sensitive=options.get('case_sensitive', False),
                        regex=options.get('regex', False)
                    )
                    
                    if search_results:
                        matches = sum(len(quads) for _, quads in search_results)
                        result['keywords_found'][keyword] = matches
                        
                        # 添加高亮
                        color = validated['colors'][i % len(validated['colors'])]
                        opacity = options.get('opacity', 0.5)
                        occurrence = options.get('occurrence')
                        
                        count = highlighter.add_highlights(
                            search_results,
                            color=color,
                            opacity=opacity,
                            occurrence=occurrence
                        )
                        
                        total_highlights += count
                        self.logger.info(f"关键词 '{keyword}': {matches} 个匹配项, {count} 个高亮")
                    else:
                        result['keywords_found'][keyword] = 0
                        self.logger.info(f"关键词 '{keyword}': 未找到匹配项")
                
                # 保存文件
                if total_highlights > 0:
                    highlighter.save_pdf(str(output_path))
                    result['highlights_added'] = total_highlights
                    result['success'] = True
                    self.logger.info(f"处理完成: {output_path}")
                else:
                    self.logger.warning("未找到任何匹配项，不生成输出文件")
            
            self.stats['processed_files'] += 1
            self.stats['total_highlights'] += result['highlights_added']
            
        except Exception as e:
            result['error'] = str(e)
            self.stats['errors'] += 1
            self.logger.error(f"处理文件失败 {pdf_path}: {e}")
        
        return result
    
    def batch_process(self, file_list: List[str], keywords: List[str], 
                     output_dir: str = None, **options) -> List[Dict[str, Any]]:
        """批量处理PDF文件
        
        Args:
            file_list: PDF文件路径列表
            keywords: 关键词列表
            output_dir: 输出目录
            **options: 其他选项
            
        Returns:
            List[Dict]: 处理结果列表
        """
        self.stats['start_time'] = time.time()
        self.logger.info(f"开始批量处理 {len(file_list)} 个文件")
        
        results = []
        
        # 创建输出目录
        if output_dir:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        for i, pdf_path in enumerate(file_list, 1):
            self.logger.info(f"处理文件 {i}/{len(file_list)}: {pdf_path}")
            
            # 生成输出路径
            output_path = None
            if output_dir:
                input_path = Path(pdf_path)
                output_path = Path(output_dir) / f"{input_path.stem}_highlighted{input_path.suffix}"
            
            # 处理文件
            result = self.process_single_file(pdf_path, keywords, str(output_path), **options)
            results.append(result)
            
            # 显示进度
            if i % 10 == 0 or i == len(file_list):
                self.logger.info(f"进度: {i}/{len(file_list)} 完成")
        
        self.stats['end_time'] = time.time()
        self._print_summary(results)
        
        return results
    
    def _print_summary(self, results: List[Dict[str, Any]]) -> None:
        """打印处理摘要"""
        elapsed_time = self.stats['end_time'] - self.stats['start_time']
        
        print("\n" + "=" * 60)
        print("批量处理摘要")
        print("=" * 60)
        print(f"处理文件数: {self.stats['processed_files']}")
        print(f"成功文件数: {sum(1 for r in results if r['success'])}")
        print(f"失败文件数: {self.stats['errors']}")
        print(f"总高亮数: {self.stats['total_highlights']}")
        print(f"处理耗时: {elapsed_time:.2f}秒")
        
        if self.stats['errors'] > 0:
            print("\n失败文件:")
            for result in results:
                if not result['success']:
                    print(f"  {result['input_file']}: {result['error']}")


def configuration_example():
    """配置管理示例"""
    print("=== 配置管理示例 ===")
    
    # 获取配置
    settings = get_settings()
    
    # 显示默认配置
    print("默认配置:")
    print(f"  默认颜色: {get_config('highlight.default_color')}")
    print(f"  默认透明度: {get_config('highlight.default_opacity')}")
    print(f"  最大关键词长度: {get_config('search.max_keyword_length')}")
    print(f"  输出文件后缀: {get_config('files.output_suffix')}")
    
    # 修改配置
    from pdf_highlighter.config.settings import set_config
    set_config('highlight.default_color', (0, 1, 0))  # 绿色
    set_config('highlight.default_opacity', 0.8)
    
    print("\n修改后的配置:")
    print(f"  默认颜色: {get_config('highlight.default_color')}")
    print(f"  默认透明度: {get_config('highlight.default_opacity')}")


def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    logger = get_logger()
    
    # 测试各种错误情况
    test_cases = [
        ("不存在的文件.pdf", "测试"),
        ("test.pdf", ""),  # 空关键词
    ]
    
    for pdf_path, keyword in test_cases:
        try:
            if not keyword:
                raise SearchError("关键词不能为空")
            
            # 尝试验证PDF路径
            ParameterValidator.validate_pdf_path(pdf_path)
            
        except (PDFError, SearchError) as e:
            logger.error(f"预期错误: {e}")
            print(f"  捕获错误: {type(e).__name__}: {e}")
        except Exception as e:
            logger.error(f"未预期错误: {e}")
            print(f"  未预期错误: {e}")


def performance_monitoring_example():
    """性能监控示例"""
    print("\n=== 性能监控示例 ===")
    
    from pdf_highlighter.utils.logger import log_performance
    
    @log_performance
    def simulate_processing():
        """模拟处理过程"""
        time.sleep(0.1)  # 模拟处理时间
        return "处理完成"
    
    # 执行带性能监控的函数
    result = simulate_processing()
    print(f"  结果: {result}")


def main():
    """主函数"""
    print("PDF高亮工具高级使用示例")
    print("=" * 60)
    
    # 设置日志文件
    log_file = project_root / "examples" / "advanced_example.log"
    
    # 创建高级处理器
    processor = AdvancedPDFProcessor(str(log_file))
    
    # 检查测试文件
    test_file = project_root / "test.pdf"
    if test_file.exists():
        print(f"\n使用测试文件: {test_file}")
        
        # 单文件处理示例
        keywords = ["前端", "工程师", "开发"]
        output_dir = project_root / "examples" / "advanced_output"
        
        result = processor.process_single_file(
            str(test_file),
            keywords,
            output_path=str(output_dir / "advanced_highlighted.pdf"),
            case_sensitive=False,
            regex=False,
            opacity=0.6
        )
        
        print(f"\n单文件处理结果:")
        print(f"  成功: {result['success']}")
        print(f"  高亮数: {result['highlights_added']}")
        print(f"  关键词匹配: {result['keywords_found']}")
        
        # 批量处理示例（使用同一个文件模拟）
        file_list = [str(test_file)] * 3  # 模拟3个文件
        batch_results = processor.batch_process(
            file_list,
            keywords,
            str(output_dir),
            opacity=0.7
        )
    
    # 其他示例
    configuration_example()
    error_handling_example()
    performance_monitoring_example()
    
    print(f"\n日志文件: {log_file}")
    print("高级示例执行完成！")


if __name__ == "__main__":
    main()
