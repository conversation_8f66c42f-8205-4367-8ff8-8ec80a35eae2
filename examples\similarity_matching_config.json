{"description": "上下文相似度匹配功能配置示例", "version": "2.1", "keywords": [{"id": "review_exact", "text": "审核", "color": "red", "opacity": 0.8, "context_phrase": "添加以供审核", "similarity_threshold": 1.0, "priority": 1, "comment": "精确匹配：只匹配完全相同的上下文"}, {"id": "review_fuzzy_high", "text": "审核", "color": "orange", "opacity": 0.7, "context_phrase": "添加以供审核", "similarity_threshold": 0.9, "priority": 2, "comment": "高相似度匹配：处理轻微的文本差异"}, {"id": "review_fuzzy_medium", "text": "审核", "color": "yellow", "opacity": 0.6, "context_phrase": "添加以供审核", "similarity_threshold": 0.8, "priority": 3, "comment": "中等相似度匹配：处理多空格、少字等情况"}, {"id": "submit_with_spaces", "text": "提交", "color": "blue", "opacity": 0.7, "context_phrase": "确认无误后提交", "similarity_threshold": 0.7, "priority": 1, "comment": "处理可能的文本变化：'请确认无误后提交'"}, {"id": "app_context_fuzzy", "text": "应用", "color": "green", "opacity": 0.6, "context_phrase": "苹果ipa应用", "similarity_threshold": 0.85, "priority": 1, "comment": "处理应用相关的上下文变化"}, {"id": "global_threshold_test", "text": "测试", "color": "purple", "opacity": 0.5, "context_phrase": "这是测试文本", "priority": 2, "comment": "使用全局相似度阈值"}, {"id": "no_context_keyword", "text": "普通关键词", "color": "cyan", "opacity": 0.4, "priority": 3, "comment": "无上下文限制的普通关键词"}], "global_options": {"context_similarity_threshold": 0.8, "context_fallback_strategy": "fallback_to_normal", "output_suffix": "_similarity_demo"}, "similarity_matching_guide": {"threshold_recommendations": {"1.0": "精确匹配 - 完全相同的文本", "0.9-0.95": "高相似度 - 处理轻微的格式差异", "0.8-0.89": "中等相似度 - 处理多空格、标点差异", "0.7-0.79": "低相似度 - 处理多字/少字的情况", "0.6-0.69": "很低相似度 - 可能产生误匹配，谨慎使用"}, "common_scenarios": [{"scenario": "PDF文本提取多空格", "original": "添加以供审核", "pdf_text": "添加 以供 审核", "recommended_threshold": 0.8, "expected_similarity": 0.85}, {"scenario": "PDF文本提取少字", "original": "请确认无误后提交", "pdf_text": "确认无误后提交", "recommended_threshold": 0.7, "expected_similarity": 0.75}, {"scenario": "PDF文本提取多字", "original": "苹果应用", "pdf_text": "苹果ipa应用", "recommended_threshold": 0.8, "expected_similarity": 0.82}], "algorithm_details": {"normalization": "自动移除多余空白字符", "similarity_method": "SequenceMatcher (编辑距离)", "text_splitting": "智能短语分割，支持滑动窗口匹配", "performance": "针对中文文本优化"}}, "usage_examples": {"config_file": {"description": "配置文件方式使用相似度匹配", "steps": ["1. 设置全局相似度阈值：context_similarity_threshold", "2. 为特定关键词设置局部阈值：similarity_threshold", "3. 配置上下文短语：context_phrase", "4. 运行处理并查看匹配日志"]}, "programming_api": {"description": "编程API方式使用相似度匹配", "example_code": ["highlighter = MultiKeywordHighlighter('file.pdf')", "highlighter.global_similarity_threshold = 0.8", "highlighter.add_keyword(", "    '审核',", "    color='red',", "    context_phrase='添加以供审核',", "    similarity_threshold=0.9", ")", "result = highlighter.process()"]}}, "expected_results": {"log_output": ["INFO - 设置全局相似度阈值: 0.8", "DEBUG - 使用相似度阈值: 0.9", "INFO - 在页面 3 找到相似上下文 (相似度: 0.850): '添加 以供 审核'", "INFO - ✅ [review_fuzzy_high] '审核': 添加 2 个高亮"], "processing_results": {"exact_match": "找到精确匹配，使用原有逻辑", "fuzzy_match": "找到相似匹配，记录相似度信息", "no_match": "未找到相似匹配，执行降级策略", "fallback": "降级为普通关键字搜索"}}, "troubleshooting": {"common_issues": [{"issue": "相似度阈值太高，无法匹配", "solution": "降低阈值到0.7-0.8范围", "example": "similarity_threshold: 0.8"}, {"issue": "相似度阈值太低，误匹配过多", "solution": "提高阈值到0.85-0.9范围", "example": "similarity_threshold: 0.85"}, {"issue": "上下文短语过长或过短", "solution": "调整上下文短语长度，建议3-10个字符", "example": "context_phrase: '添加以供审核'"}]}}